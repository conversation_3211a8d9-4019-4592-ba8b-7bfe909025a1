/// AI客户端模块 (Windows 7兼容版本)
/// 整合加密和HTTP通信，提供高级AI交互接口
/// 使用同步API以避免tokio依赖问题

use crate::config::AppConfig;
use crate::encryption::Encryptor;
use crate::http_client::{HttpClient, create_text_request, create_image_request, create_gemini_request, create_gemini_detailed_request, handle_api_response};
use std::sync::{Arc, Mutex};

/// AI客户端状态
#[derive(Debug, Clone)]
pub enum ClientStatus {
    Idle,           // 空闲
    Connecting,     // 连接中
    Sending,        // 发送中
    Waiting,        // 等待响应
    Processing,     // 处理响应
    Error(String),  // 错误状态
}

impl ClientStatus {
    pub fn to_string(&self) -> String {
        match self {
            ClientStatus::Idle => "空闲".to_string(),
            ClientStatus::Connecting => "连接中...".to_string(),
            ClientStatus::Sending => "发送中...".to_string(),
            ClientStatus::Waiting => "等待响应...".to_string(),
            ClientStatus::Processing => "处理响应...".to_string(),
            ClientStatus::Error(msg) => format!("错误: {}", msg),
        }
    }
}

/// AI客户端 (同步版本)
pub struct AiClient {
    config: AppConfig,
    encryptor: Encryptor,
    http_client: HttpClient,
    status: Arc<Mutex<ClientStatus>>,
}

impl AiClient {
    /// 创建新的AI客户端 (同步版本)
    pub fn new(config: AppConfig) -> Result<Self, String> {
        // 验证配置
        config.validate()?;

        // 创建加密器
        let encryptor = Encryptor::new(
            &config.encryption_key,
            &config.encryption_iv,
            config.debug
        )?;

        // 创建HTTP客户端
        let http_client = HttpClient::new(
            &config.server_url,
            config.timeout_seconds,
            config.debug
        )?;

        let client = Self {
            config,
            encryptor,
            http_client,
            status: Arc::new(Mutex::new(ClientStatus::Idle)),
        };

        // 测试连接
        client.set_status(ClientStatus::Connecting);
        match client.http_client.test_connection() {
            Ok(_) => {
                client.set_status(ClientStatus::Idle);
                println!("🤖 AI客户端初始化成功");
            }
            Err(e) => {
                let error_msg = format!("服务器连接失败: {}", e);
                client.set_status(ClientStatus::Error(error_msg.clone()));
                return Err(error_msg);
            }
        }

        Ok(client)
    }
    
    /// 设置客户端状态 (同步版本)
    fn set_status(&self, status: ClientStatus) {
        if let Ok(mut current_status) = self.status.lock() {
            *current_status = status;
        }
    }

    /// 获取客户端状态 (同步版本)
    pub fn get_status(&self) -> ClientStatus {
        if let Ok(status) = self.status.lock() {
            status.clone()
        } else {
            ClientStatus::Error("状态锁定失败".to_string())
        }
    }
    
    /// 发送文本消息 (同步版本)
    pub fn send_text(&self, text: &str) -> Result<String, String> {
        if text.is_empty() {
            return Err("文本内容不能为空".to_string());
        }

        println!("📝 准备发送文本消息...");
        if self.config.debug {
            println!("   文本内容: {}", text);
        }

        // 更新状态
        self.set_status(ClientStatus::Sending);

        // 加密文本
        let encrypted_text = self.encryptor.encrypt(text)
            .map_err(|e| {
                let error_msg = format!("文本加密失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;
        
        // 创建请求
        let request = create_text_request(encrypted_text);

        // 发送请求
        self.set_status(ClientStatus::Waiting);
        let response = self.http_client.send_chat_request(request)
            .map_err(|e| {
                let error_msg = format!("发送请求失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;
        
        // 处理响应
        self.set_status(ClientStatus::Processing);
        let encrypted_response = handle_api_response(response)
            .map_err(|e| {
                let error_msg = format!("响应处理失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 解密响应
        let decrypted_response = self.encryptor.decrypt(&encrypted_response)
            .map_err(|e| {
                let error_msg = format!("响应解密失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 恢复空闲状态
        self.set_status(ClientStatus::Idle);

        println!("✅ 文本消息发送成功");
        Ok(decrypted_response)
    }

    /// 发送文本消息给Gemini (同步版本)
    pub fn send_gemini_text(&self, text: &str) -> Result<String, String> {
        if text.is_empty() {
            return Err("文本内容不能为空".to_string());
        }

        println!("🤖 准备发送文本消息给Gemini...");
        if self.config.debug {
            println!("   文本内容: {}", text);
        }

        // 更新状态
        self.set_status(ClientStatus::Sending);

        // 加密文本
        let encrypted_text = self.encryptor.encrypt(text)
            .map_err(|e| {
                let error_msg = format!("文本加密失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 创建Gemini请求
        let request = create_gemini_request(encrypted_text);

        // 发送请求
        self.set_status(ClientStatus::Waiting);
        let response = self.http_client.send_chat_request(request)
            .map_err(|e| {
                let error_msg = format!("发送请求失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 处理响应
        self.set_status(ClientStatus::Processing);
        let encrypted_response = handle_api_response(response)
            .map_err(|e| {
                let error_msg = format!("响应处理失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 解密响应
        let decrypted_response = self.encryptor.decrypt(&encrypted_response)
            .map_err(|e| {
                let error_msg = format!("响应解密失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 恢复空闲状态
        self.set_status(ClientStatus::Idle);

        println!("✅ Gemini文本消息发送成功");
        Ok(decrypted_response)
    }

    /// 发送详细文本消息给Gemini (同步版本)
    pub fn send_gemini_detailed_text(&self, text: &str) -> Result<String, String> {
        if text.is_empty() {
            return Err("文本内容不能为空".to_string());
        }

        println!("🤖 准备发送详细文本消息给Gemini...");
        if self.config.debug {
            println!("   文本内容: {}", text);
        }

        // 更新状态
        self.set_status(ClientStatus::Sending);

        // 加密文本
        let encrypted_text = self.encryptor.encrypt(text)
            .map_err(|e| {
                let error_msg = format!("文本加密失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 创建Gemini详细请求
        let request = create_gemini_detailed_request(encrypted_text);

        // 发送请求
        self.set_status(ClientStatus::Waiting);
        let response = self.http_client.send_chat_request(request)
            .map_err(|e| {
                let error_msg = format!("发送请求失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 处理响应
        self.set_status(ClientStatus::Processing);
        let encrypted_response = handle_api_response(response)
            .map_err(|e| {
                let error_msg = format!("响应处理失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 解密响应
        let decrypted_response = self.encryptor.decrypt(&encrypted_response)
            .map_err(|e| {
                let error_msg = format!("响应解密失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 恢复空闲状态
        self.set_status(ClientStatus::Idle);

        println!("✅ Gemini详细文本消息发送成功");
        Ok(decrypted_response)
    }

    /// 发送图片（Base64编码） (同步版本)
    pub fn send_image(&self, image_base64: &str) -> Result<String, String> {
        if image_base64.is_empty() {
            return Err("图片数据不能为空".to_string());
        }

        println!("🖼️ 准备发送图片数据...");
        if self.config.debug {
            println!("   图片数据长度: {} 字节", image_base64.len());
        }

        // 更新状态
        self.set_status(ClientStatus::Sending);

        // 加密图片数据
        let encrypted_image = self.encryptor.encrypt(image_base64)
            .map_err(|e| {
                let error_msg = format!("图片数据加密失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;
        
        // 创建请求
        let request = create_image_request(encrypted_image);

        // 发送请求
        self.set_status(ClientStatus::Waiting);
        let response = self.http_client.send_chat_request(request)
            .map_err(|e| {
                let error_msg = format!("发送图片请求失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 处理响应
        self.set_status(ClientStatus::Processing);
        let encrypted_response = handle_api_response(response)
            .map_err(|e| {
                let error_msg = format!("图片响应处理失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 解密响应
        let decrypted_response = self.encryptor.decrypt(&encrypted_response)
            .map_err(|e| {
                let error_msg = format!("图片响应解密失败: {}", e);
                self.set_status(ClientStatus::Error(error_msg.clone()));
                error_msg
            })?;

        // 恢复空闲状态
        self.set_status(ClientStatus::Idle);

        println!("✅ 图片数据发送成功");
        Ok(decrypted_response)
    }
    
    /// 测试加密功能
    pub fn test_encryption(&self) -> Result<(), String> {
        println!("🧪 测试加密功能...");
        self.encryptor.test()
    }
    
    /// 获取配置信息
    pub fn get_config(&self) -> &AppConfig {
        &self.config
    }
}
