[package]
name = "rust_uiaccess"
version = "0.1.0"
edition = "2021"

# Windows 7兼容性配置
[target.'cfg(windows)']
# 使用Windows 7兼容的依赖版本

[dependencies]
# Windows API - 使用兼容Windows 7的版本
windows = { version = "0.48", features = [
    "Win32_Foundation",
    "Win32_Security",
    "Win32_Security_Authorization",
    "Win32_System_Threading",
    "Win32_System_Diagnostics_ToolHelp",
    "Win32_UI_WindowsAndMessaging",
    "Win32_Graphics_Gdi",
    "Win32_System_LibraryLoader",
    "Win32_System_Environment",
    "Win32_System_ProcessStatus",
    "Win32_System_Memory",
    "Win32_UI_Controls",
    "Win32_UI_Input_KeyboardAndMouse",
    "Win32_Graphics_Direct2D",
    "Win32_Graphics_DirectWrite",
    "Win32_System_Com",
    "Win32_System_Registry",  # 添加注册表支持
    "Win32_Storage_FileSystem",  # 添加文件系统支持
    "Win32_System_SystemInformation",  # 添加系统信息支持用于版本检测
    "Win32_Networking_WinHttp"  # 添加WinHTTP支持用于网络请求
]}

# 日志和追踪 - 使用兼容版本
tracing = "0.1.40"
tracing-subscriber = "0.3.18"
log = "0.4"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# HTTP客户端 - Windows 7兼容版本
# 使用基于Windows API的HTTP实现，避免复杂依赖
# 添加WinHTTP API支持

# 加密支持 - 使用Rust 1.75兼容版本
aes-gcm = "0.10.2"  # 使用兼容版本
base64 = "0.21.0"   # 使用兼容版本

# 剪切板支持 - 确保Windows 7兼容
arboard = "3.2.0"   # 使用兼容版本

# 截图支持 - 轻量级PNG编码
png = "0.17.7"      # 使用兼容版本

[lib]
name = "uiaccess_lib"
path = "src/lib.rs"

# 设置为 Windows 子系统，不显示控制台
[[bin]]
name = "uiaccess_app"
path = "src/main.rs"

# Windows 7兼容性编译配置
[target.x86_64-win7-windows-msvc]
# 64位Windows 7 MSVC目标

[target.i686-win7-windows-msvc]
# 32位Windows 7 MSVC目标

[profile.release]
opt-level = "z"     # 优化包体大小
lto = true          # 链接时优化
codegen-units = 1   # 减少代码生成单元
panic = "abort"     # 减小panic处理代码
strip = true        # 移除调试符号

# Windows 7兼容性优化
[profile.release.package."*"]
opt-level = "z"
