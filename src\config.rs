/// 应用配置模块
/// 包含服务器连接和加密相关的配置信息

/// 应用配置结构
#[derive(Debug, <PERSON><PERSON>)]
pub struct AppConfig {
    /// 服务器基础URL
    pub server_url: String,
    /// AES-256加密密钥（32字节）
    pub encryption_key: String,
    /// AES-GCM加密IV/Nonce（12字节）
    pub encryption_iv: String,
    /// 请求超时时间（秒）
    pub timeout_seconds: u64,
    /// 是否启用调试模式
    pub debug: bool,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            // 默认本地服务器地址
            server_url: "http://34.80.43.163:8080".to_string(),
            
            // 默认加密密钥（与后端保持一致）
            // 注意：生产环境中应该使用更安全的密钥
            encryption_key: "3f02a704-dc1f-4dd8-8f02-590280257997".to_string(),
            
            // 默认IV值（与后端保持一致）
            // GCM模式需要12字节的nonce
            encryption_iv: "fixed-iv-value-16byte".to_string(),
            
            // 默认30秒超时
            timeout_seconds: 300,
            
            // 默认关闭调试模式
            debug: false,
        }
    }
}

impl AppConfig {
    /// 创建新的配置实例
    pub fn new() -> Self {
        Self::default()
    }
    
    /// 设置服务器URL
    pub fn with_server_url(mut self, url: &str) -> Self {
        self.server_url = url.to_string();
        self
    }
    
    /// 设置加密密钥
    pub fn with_encryption_key(mut self, key: &str) -> Self {
        self.encryption_key = key.to_string();
        self
    }
    
    /// 设置加密IV
    pub fn with_encryption_iv(mut self, iv: &str) -> Self {
        self.encryption_iv = iv.to_string();
        self
    }
    
    /// 设置超时时间
    pub fn with_timeout(mut self, seconds: u64) -> Self {
        self.timeout_seconds = seconds;
        self
    }
    
    /// 启用调试模式
    pub fn with_debug(mut self, debug: bool) -> Self {
        self.debug = debug;
        self
    }
    
    /// 获取完整的API端点URL
    pub fn get_chat_url(&self) -> String {
        format!("{}/api/chat", self.server_url)
    }
    
    /// 获取健康检查URL
    pub fn get_health_url(&self) -> String {
        format!("{}/api/health", self.server_url)
    }
    
    /// 验证配置是否有效
    pub fn validate(&self) -> Result<(), String> {
        // 检查服务器URL
        if self.server_url.is_empty() {
            return Err("服务器URL不能为空".to_string());
        }
        
        // 检查加密密钥长度（至少32字节）
        if self.encryption_key.len() < 32 {
            return Err("加密密钥长度不足，至少需要32字节".to_string());
        }
        
        // IV长度检查已移除，因为加密器会自动处理短IV（用0填充）
        
        // 检查超时时间
        if self.timeout_seconds == 0 {
            return Err("超时时间必须大于0".to_string());
        }
        
        Ok(())
    }
    
    /// 从环境变量加载配置（如果存在）
    pub fn load_from_env(mut self) -> Self {
        // 从环境变量覆盖配置
        if let Ok(url) = std::env::var("SERVER_URL") {
            self.server_url = url;
        }
        
        if let Ok(key) = std::env::var("ENCRYPTION_KEY") {
            self.encryption_key = key;
        }
        
        if let Ok(iv) = std::env::var("ENCRYPTION_IV") {
            self.encryption_iv = iv;
        }
        
        if let Ok(timeout) = std::env::var("TIMEOUT_SECONDS") {
            if let Ok(seconds) = timeout.parse::<u64>() {
                self.timeout_seconds = seconds;
            }
        }
        
        if let Ok(debug) = std::env::var("DEBUG") {
            self.debug = debug.to_lowercase() == "true";
        }
        
        self
    }
    
    /// 打印配置信息（隐藏敏感信息）
    pub fn print_info(&self) {
        println!("📋 应用配置信息:");
        println!("   服务器URL: {}", self.server_url);
        println!("   加密密钥: {}***（已隐藏）", &self.encryption_key[..4.min(self.encryption_key.len())]);
        println!("   加密IV: {}***（已隐藏）", &self.encryption_iv[..2.min(self.encryption_iv.len())]);
        println!("   超时时间: {}秒", self.timeout_seconds);
        println!("   调试模式: {}", if self.debug { "开启" } else { "关闭" });
    }
}

/// 生产环境配置示例
pub fn production_config() -> AppConfig {
    AppConfig::new()
        .with_server_url("http://34.80.43.163:8080")
        .with_encryption_key("3f02a704-dc1f-4dd8-8f02-590280257997")
        .with_encryption_iv("fixed-iv-value-16byte")
        .with_timeout(300)
        .with_debug(false)
}

/// 开发环境配置示例
pub fn development_config() -> AppConfig {
    AppConfig::new()
        .with_server_url("http://34.80.43.163:8080")
        .with_encryption_key("3f02a704-dc1f-4dd8-8f02-590280257997")  // 与后端匹配的密钥
        .with_encryption_iv("fixed-iv-value-16byte")  // 与后端匹配的IV
        .with_timeout(300)
        .with_debug(true)
}
