// process.rs - Process manipulation functions (Windows 7兼容版本)
use crate::{Result, UiAccessError, is_uiaccess_supported};
use windows::{
    core::{PCWSTR, PWSTR},
    Win32::{
        Foundation::{CloseHandle, BOOL, HANDLE, MAX_PATH, FALSE},
        Security::{
            DuplicateTokenEx, TokenImpersonation,
            LookupPrivilegeValueW, PRIVILEGE_SET, SE_TCB_NAME,
            TOKEN_QUERY, TOKEN_DUPLICATE, TokenSessionId, PrivilegeCheck, GetTokenInformation,
            SecurityImpersonation,
        },
        System::{
            Diagnostics::ToolHelp::{
                CreateToolhelp32Snapshot, Process32FirstW, Process32NextW, PROCESSENTRY32W,
                TH32CS_SNAPPROCESS,
            },
            Threading::{
                CreateProcessAsUserW, PROCESS_INFORMATION,
                PROCESS_QUERY_LIMITED_INFORMATION, STARTUPINFOW, PROCESS_CREATION_FLAGS,
                OpenProcess, OpenProcessToken, ExitProcess,
            },
            Environment::{GetCurrentDirectoryW},
        },
    },
};
use std::mem;

// Duplicate winlogon token with necessary privileges (Windows 7兼容版本)
pub fn duplicate_winlogon_token(session_id: u32) -> Result<HANDLE> {
    // 在Windows 7上，这个功能可能不完全可用，但我们仍然尝试
    if !is_uiaccess_supported() {
        println!("⚠️ 在Windows 7上尝试获取winlogon令牌，可能失败");
    }
    // Set up privilege set for SeTcbPrivilege check
    let mut ps = PRIVILEGE_SET {
        PrivilegeCount: 1,
        Control: 1, // PRIVILEGE_SET_ALL_NECESSARY value is 1
        Privilege: [unsafe { mem::zeroed() }],
    };
    
    let result = unsafe { LookupPrivilegeValueW(PCWSTR::null(), SE_TCB_NAME, &mut ps.Privilege[0].Luid) };
    if !result.as_bool() {
        return Err(UiAccessError::PrivilegeError(windows::core::Error::from_win32().code().0 as u32));
    }

    // Create process snapshot
    let snapshot = unsafe { CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0) };
    let snapshot = match snapshot {
        Ok(handle) => {
            if handle.is_invalid() {
                return Err(UiAccessError::ProcessError(windows::core::Error::from_win32().code().0 as u32));
            }
            handle
        },
        Err(e) => return Err(UiAccessError::ProcessError(e.code().0 as u32)),
    };

    // Iterate through processes to find winlogon.exe
    let mut pe = PROCESSENTRY32W {
        dwSize: std::mem::size_of::<PROCESSENTRY32W>() as u32,
        ..Default::default()
    };
    
    let mut found = false;
    let mut token_handle = HANDLE::default();
    let mut error_code = 0;

    let result = unsafe { Process32FirstW(snapshot, &mut pe) };
    if !result.as_bool() {
        unsafe { CloseHandle(snapshot).ok(); }
        return Err(UiAccessError::ProcessError(windows::core::Error::from_win32().code().0 as u32));
    }

    loop {
        // Check if this is winlogon.exe
        let len = (0..MAX_PATH as usize)
            .position(|i| pe.szExeFile[i] == 0)
            .unwrap_or(MAX_PATH as usize);
        let exe_name = String::from_utf16_lossy(&pe.szExeFile[..len]);

        if exe_name.to_lowercase() == "winlogon.exe" {
            // Open the process
            let process = unsafe { 
                OpenProcess(
                    PROCESS_QUERY_LIMITED_INFORMATION, 
                    FALSE, 
                    pe.th32ProcessID
                )
            };

            let process = match process {
                Ok(handle) => {
                    if handle.is_invalid() {
                        continue;
                    }
                    handle
                },
                Err(_) => continue,
            };
            
            // Open process token
            let mut process_token = HANDLE::default();
            let result = unsafe { 
                OpenProcessToken(
                    process, 
                    TOKEN_QUERY | TOKEN_DUPLICATE, 
                    &mut process_token
                )
            };

            if result.as_bool() {
                // Check if token has SeTcbPrivilege
                let mut privilege_result = FALSE;
                let privilege_check_result = unsafe {
                    PrivilegeCheck(
                        process_token,
                        &mut ps,
                        &mut privilege_result as *mut BOOL as *mut i32,
                    )
                };

                if privilege_check_result.as_bool() && privilege_result.as_bool() {
                    // Check if this is the correct session
                    let mut token_session_id: u32 = 0;
                    let mut return_length: u32 = 0;
                    let session_result = unsafe {
                        GetTokenInformation(
                            process_token,
                            TokenSessionId,
                            Some(&mut token_session_id as *mut u32 as *mut _),
                            std::mem::size_of::<u32>() as u32,
                            &mut return_length,
                        )
                    };

                    if session_result.as_bool() && token_session_id == session_id {
                        found = true;
                        // Duplicate the token
                        let result = unsafe {
                            DuplicateTokenEx(
                                process_token,
                                windows::Win32::Security::TOKEN_IMPERSONATE,
                                None, // Security attributes
                                SecurityImpersonation, // 正确使用SECURITY_IMPERSONATION_LEVEL枚举值
                                TokenImpersonation,
                                &mut token_handle,
                            )
                        };

                        if !result.as_bool() {
                            error_code = windows::core::Error::from_win32().code().0 as u32;
                        }
                    }
                }
                unsafe { CloseHandle(process_token); }
            }
            unsafe { CloseHandle(process).ok(); }
        }

        if found {
            break;
        }

        // Move to next process
        let result = unsafe { Process32NextW(snapshot, &mut pe) };
        if !result.as_bool() {
            break;
        }
    }

    unsafe { CloseHandle(snapshot).ok(); }

    if found {
        if token_handle.is_invalid() {
            Err(UiAccessError::TokenError(error_code))
        } else {
            Ok(token_handle)
        }
    } else {
        Err(UiAccessError::ProcessError(windows::Win32::Foundation::ERROR_NOT_FOUND.0 as u32))
    }
}

// Restart current process with the given token
pub fn restart_with_token(token: HANDLE) -> Result<()> {
    let current_exe = std::env::current_exe()
        .map_err(|_| UiAccessError::ProcessError(1))?;
    
    let cmd_line = format!("\"{}\"", current_exe.display());
    let mut cmd_wide: Vec<u16> = cmd_line.encode_utf16().chain(std::iter::once(0)).collect();
    
    let mut startup_info = STARTUPINFOW::default();
    startup_info.cb = std::mem::size_of::<STARTUPINFOW>() as u32;
    
    let mut process_info = PROCESS_INFORMATION::default();
    
    // Get current directory
    let mut current_dir_buf = [0u16; 260]; // MAX_PATH
    // 修复GetCurrentDirectoryW函数调用，它只接受一个参数Option<&mut [u16]>
    let current_dir_result = unsafe { GetCurrentDirectoryW(Some(&mut current_dir_buf)) };
    if current_dir_result == 0 {
        return Err(UiAccessError::ProcessError(windows::core::Error::from_win32().code().0 as u32));
    }
    
    let result = unsafe {
        CreateProcessAsUserW(
            token,
            PCWSTR::null(),
            PWSTR(cmd_wide.as_mut_ptr()),
            None, // Process security attributes
            None, // Thread security attributes
            BOOL(0), // Don't inherit handles
            PROCESS_CREATION_FLAGS(0), // Creation flags
            None, // Environment
            PCWSTR(current_dir_buf.as_ptr()), // Current directory
            &startup_info,
            &mut process_info,
        )
    };
    
    if !result.as_bool() {
        return Err(UiAccessError::ProcessError(windows::core::Error::from_win32().code().0 as u32));
    }
    
    // Clean up process and thread handles
    unsafe {
        CloseHandle(process_info.hProcess).ok();
        CloseHandle(process_info.hThread).ok();
    }
    
    // Exit current process
    unsafe {
        ExitProcess(0);
    }
    
    // This is unreachable, but keeps the compiler happy
    #[allow(unreachable_code)]
    Ok(())
}
