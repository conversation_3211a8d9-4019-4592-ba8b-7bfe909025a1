/// HTTP客户端模块 (Windows 7兼容版本)
/// 负责与后端API进行通信
/// 使用简单的TCP socket实现，避免复杂的第三方依赖

use serde::{Deserialize, Serialize};
use std::time::Duration;
use std::io::{Read, Write};
use std::net::{TcpStream, ToSocketAddrs};

/// 请求类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum RequestType {
    Text,
    Image,
    Gemini,
    #[serde(rename = "gemini-img")]
    GeminiImg,
}

/// API请求结构
#[derive(Debug, Serialize)]
pub struct ApiRequest {
    #[serde(rename = "type")]
    pub request_type: RequestType,
    pub content: String, // 加密后的内容
}

/// API响应结构
#[derive(Debug, Deserialize)]
pub struct ApiResponse {
    pub success: bool,
    pub message: Option<String>,
    pub data: Option<String>, // 加密后的响应数据
}

/// HTTP客户端 (Windows 7兼容版本 - 基于WinHTTP API)
pub struct HttpClient {
    base_url: String,
    timeout: Duration,
    debug: bool,
    user_agent: String,
}

impl HttpClient {
    /// 创建新的HTTP客户端 (Windows 7兼容版本 - 基于WinHTTP API)
    pub fn new(base_url: &str, timeout_seconds: u64, debug: bool) -> Result<Self, String> {
        let timeout = Duration::from_secs(timeout_seconds);
        let user_agent = "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36".to_string();

        if debug {
            println!("🌐 HTTP客户端初始化成功 (Windows 7兼容版本 - WinHTTP API)");
            println!("   基础URL: {}", base_url);
            println!("   超时时间: {}秒", timeout_seconds);
            println!("   User-Agent: {}", user_agent);
        }

        Ok(Self {
            base_url: base_url.to_string(),
            timeout,
            debug,
            user_agent,
        })
    }
    
    /// 发送聊天请求 (Windows 7兼容版本 - 基于WinHTTP API)
    pub fn send_chat_request(&self, request: ApiRequest) -> Result<ApiResponse, String> {
        let url = format!("{}/api/chat", self.base_url);

        if self.debug {
            println!("📤 发送聊天请求到: {}", url);
            println!("   请求类型: {:?}", request.request_type);
            println!("   内容长度: {} 字节", request.content.len());
        }

        // 序列化请求数据
        let json_data = serde_json::to_string(&request)
            .map_err(|e| format!("序列化请求失败: {}", e))?;

        // 发送HTTP POST请求
        let response_data = self.send_http_request("POST", &url, Some(&json_data))?;

        // 解析响应
        if self.debug {
            println!("🔍 尝试解析JSON响应...");
            if response_data.len() > 200 {
                println!("   响应前200字符: {}", &response_data[..200]);
            } else {
                println!("   完整响应: {}", response_data);
            }
        }

        let api_response: ApiResponse = serde_json::from_str(&response_data)
            .map_err(|e| {
                let error_msg = format!("解析响应失败: {}. 响应内容: {}", e,
                    if response_data.len() > 500 {
                        format!("{}...", &response_data[..500])
                    } else {
                        response_data.clone()
                    });
                error_msg
            })?;

        if self.debug {
            println!("✅ 响应解析成功，success: {}", api_response.success);
            if let Some(ref data) = api_response.data {
                println!("   响应数据长度: {} 字节", data.len());
            }
        }

        Ok(api_response)
    }
    
    /// 健康检查 (Windows 7兼容版本 - 基于WinHTTP API)
    pub fn health_check(&self) -> Result<bool, String> {
        let url = format!("{}/api/health", self.base_url);

        if self.debug {
            println!("🏥 执行健康检查: {}", url);
        }

        // 发送HTTP GET请求
        match self.send_http_request("GET", &url, None) {
            Ok(_) => {
                if self.debug {
                    println!("🏥 健康检查结果: 健康");
                }
                Ok(true)
            }
            Err(e) => {
                if self.debug {
                    println!("🏥 健康检查结果: 异常 - {}", e);
                }
                Ok(false)
            }
        }
    }

    /// 测试连接 (Windows 7兼容版本 - 基于WinHTTP API)
    pub fn test_connection(&self) -> Result<(), String> {
        println!("🔗 测试服务器连接...");

        match self.health_check() {
            Ok(true) => {
                println!("✅ 服务器连接正常");
                Ok(())
            }
            Ok(false) => {
                Err("服务器返回异常状态".to_string())
            }
            Err(e) => {
                Err(format!("连接测试失败: {}", e))
            }
        }
    }

    /// 发送HTTP请求的核心实现 (基于TCP socket的简单实现)
    fn send_http_request(&self, method: &str, url: &str, body: Option<&str>) -> Result<String, String> {
        if self.debug {
            println!("🌐 发送HTTP请求: {} {}", method, url);
            if let Some(body) = body {
                println!("   请求体长度: {} 字节", body.len());
            }
        }

        // 解析URL
        let url_parts = self.parse_url(url)?;

        // 建立TCP连接
        let addr = format!("{}:{}", url_parts.host, url_parts.port);
        let mut stream = TcpStream::connect(&addr)
            .map_err(|e| format!("连接到服务器失败: {}", e))?;

        // 设置超时
        stream.set_read_timeout(Some(self.timeout))
            .map_err(|e| format!("设置读取超时失败: {}", e))?;
        stream.set_write_timeout(Some(self.timeout))
            .map_err(|e| format!("设置写入超时失败: {}", e))?;

        // 构建HTTP请求
        let mut request = format!("{} {} HTTP/1.1\r\n", method, url_parts.path);
        request.push_str(&format!("Host: {}\r\n", url_parts.host));
        request.push_str(&format!("User-Agent: {}\r\n", self.user_agent));
        request.push_str("Connection: close\r\n");

        if let Some(body) = body {
            request.push_str("Content-Type: application/json\r\n");
            request.push_str(&format!("Content-Length: {}\r\n", body.len()));
            request.push_str("\r\n");
            request.push_str(body);
        } else {
            request.push_str("\r\n");
        }

        // 发送请求
        stream.write_all(request.as_bytes())
            .map_err(|e| format!("发送请求失败: {}", e))?;

        // 读取响应
        let mut response = String::new();
        stream.read_to_string(&mut response)
            .map_err(|e| format!("读取响应失败: {}", e))?;

        if self.debug {
            println!("📥 收到响应，长度: {} 字节", response.len());
        }

        // 解析HTTP响应，提取响应体
        self.parse_http_response(&response)
    }

    /// 解析URL的辅助方法
    fn parse_url(&self, url: &str) -> Result<UrlParts, String> {
        let url = url.trim();

        let (is_https, url_without_scheme) = if url.starts_with("https://") {
            (true, &url[8..])
        } else if url.starts_with("http://") {
            (false, &url[7..])
        } else {
            return Err("URL必须以http://或https://开头".to_string());
        };

        let parts: Vec<&str> = url_without_scheme.splitn(2, '/').collect();
        let host_port = parts[0];
        let path = if parts.len() > 1 {
            format!("/{}", parts[1])
        } else {
            "/".to_string()
        };

        let (host, port) = if host_port.contains(':') {
            let host_port_parts: Vec<&str> = host_port.splitn(2, ':').collect();
            let host = host_port_parts[0].to_string();
            let port = host_port_parts[1].parse::<u16>()
                .map_err(|_| "无效的端口号".to_string())?;
            (host, port)
        } else {
            let default_port = if is_https { 443 } else { 80 };
            (host_port.to_string(), default_port)
        };

        Ok(UrlParts {
            host,
            port,
            path,
            is_https,
        })
    }

    /// 解析HTTP响应的辅助方法
    fn parse_http_response(&self, response: &str) -> Result<String, String> {
        // 查找响应头和响应体的分隔符
        if let Some(body_start) = response.find("\r\n\r\n") {
            let headers = &response[..body_start];
            let body = &response[body_start + 4..];

            // 检查状态码
            if let Some(status_line) = headers.lines().next() {
                if self.debug {
                    println!("📥 HTTP状态行: {}", status_line);
                }

                if !status_line.contains("200") && !status_line.contains("OK") {
                    return Err(format!("HTTP请求失败: {}", status_line));
                }
            }

            if self.debug {
                println!("📥 响应体前100字符: {}",
                    if body.len() > 100 { &body[..100] } else { body });
            }

            // 清理响应体，移除可能的额外字符
            let cleaned_body = body.trim();

            // 查找JSON开始位置
            if let Some(json_start) = cleaned_body.find('{') {
                let json_body = &cleaned_body[json_start..];
                if self.debug && json_start > 0 {
                    println!("🧹 检测到响应前缀，已移除: {}", &cleaned_body[..json_start]);
                }
                Ok(json_body.to_string())
            } else {
                Ok(cleaned_body.to_string())
            }
        } else {
            Err("无效的HTTP响应格式".to_string())
        }
    }
}

/// URL解析结果
struct UrlParts {
    host: String,
    port: u16,
    path: String,
    is_https: bool,
}

/// 创建文本请求
pub fn create_text_request(encrypted_content: String) -> ApiRequest {
    ApiRequest {
        request_type: RequestType::Text,
        content: encrypted_content,
    }
}

/// 创建图片请求
pub fn create_image_request(encrypted_content: String) -> ApiRequest {
    ApiRequest {
        request_type: RequestType::Image,
        content: encrypted_content,
    }
}

/// 创建Gemini文本请求
pub fn create_gemini_request(encrypted_content: String) -> ApiRequest {
    ApiRequest {
        request_type: RequestType::Gemini,
        content: encrypted_content,
    }
}

/// 创建Gemini图片请求
pub fn create_gemini_img_request(encrypted_content: String) -> ApiRequest {
    ApiRequest {
        request_type: RequestType::GeminiImg,
        content: encrypted_content,
    }
}

/// 处理API响应
pub fn handle_api_response(response: ApiResponse) -> Result<String, String> {
    if !response.success {
        let error_msg = response.message.unwrap_or_else(|| "未知错误".to_string());
        return Err(format!("服务器返回错误: {}", error_msg));
    }
    
    match response.data {
        Some(data) => Ok(data),
        None => Err("服务器响应中缺少数据".to_string()),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_create_text_request() {
        let request = create_text_request("encrypted_text".to_string());
        assert!(matches!(request.request_type, RequestType::Text));
        assert_eq!(request.content, "encrypted_text");
    }
    
    #[test]
    fn test_create_image_request() {
        let request = create_image_request("encrypted_image".to_string());
        assert!(matches!(request.request_type, RequestType::Image));
        assert_eq!(request.content, "encrypted_image");
    }
    
    #[test]
    fn test_handle_success_response() {
        let response = ApiResponse {
            success: true,
            message: None,
            data: Some("test_data".to_string()),
        };
        
        let result = handle_api_response(response).unwrap();
        assert_eq!(result, "test_data");
    }
    
    #[test]
    fn test_handle_error_response() {
        let response = ApiResponse {
            success: false,
            message: Some("Test error".to_string()),
            data: None,
        };
        
        let result = handle_api_response(response);
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Test error"));
    }
}
