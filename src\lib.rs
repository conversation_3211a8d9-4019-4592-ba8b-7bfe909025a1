// lib.rs - UIAccess library main file
mod token;
mod process;
mod privilege;
mod simple_test;

// AI客户端相关模块
mod config;
mod encryption;
mod http_client;
mod ai_client;

// 截图功能模块
mod screenshot;

pub use token::*;
pub use process::*;
pub use privilege::*;
// 导出原生窗口实现
pub use simple_test::create_simple_test_window;

// Windows版本检测
use windows::Win32::System::SystemInformation::{GetVersionExW, OSVERSIONINFOW};

use std::fmt;

#[derive(Debug)]
pub enum UiAccessError {
    TokenError(u32),
    ProcessError(u32),
    PrivilegeError(u32),
    WindowError(u32),
    Other(String),
}

impl fmt::Display for UiAccessError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            UiAccessError::TokenError(code) => write!(f, "Token error: {}", code),
            UiAccessError::ProcessError(code) => write!(f, "Process error: {}", code),
            UiAccessError::PrivilegeError(code) => write!(f, "Privilege error: {}", code),
            UiAccessError::WindowError(code) => write!(f, "Window error: {}", code),
            UiAccessError::Other(msg) => write!(f, "Error: {}", msg),
        }
    }
}

impl std::error::Error for UiAccessError {}

pub type Result<T> = std::result::Result<T, UiAccessError>;

/// Windows版本信息结构体
#[derive(Debug, Clone)]
pub struct WindowsVersion {
    pub major: u32,
    pub minor: u32,
    pub build: u32,
    pub version_name: String,
    pub supports_uiaccess: bool,
}

/// 获取详细的Windows版本信息 (改进版本检测)
pub fn get_windows_version() -> WindowsVersion {
    unsafe {
        // 首先尝试使用GetVersionExW
        let mut version_info = OSVERSIONINFOW {
            dwOSVersionInfoSize: std::mem::size_of::<OSVERSIONINFOW>() as u32,
            dwMajorVersion: 0,
            dwMinorVersion: 0,
            dwBuildNumber: 0,
            dwPlatformId: 0,
            szCSDVersion: [0; 128],
        };

        let mut major = 6u32;
        let mut minor = 1u32;
        let mut build = 0u32;

        if GetVersionExW(&mut version_info).as_bool() {
            major = version_info.dwMajorVersion;
            minor = version_info.dwMinorVersion;
            build = version_info.dwBuildNumber;

            println!("🔍 GetVersionExW 报告版本: {}.{}.{}", major, minor, build);
        } else {
            println!("⚠️ GetVersionExW 失败，使用默认版本检测");
        }

        // 如果检测到的版本看起来不对（比如在新系统上报告6.2），尝试通过构建号推断
        let (corrected_version, version_name, supports_uiaccess) = if major == 6 && minor == 2 && build > 10000 {
            // 构建号大于10000通常表示Windows 10+
            println!("🔧 检测到版本兼容性问题，通过构建号修正版本信息");

            let corrected_name = match build {
                20348..=22000 => "Windows Server 2022".to_string(),
                19041..=19044 => "Windows 10 20H1/20H2".to_string(),
                18362..=18363 => "Windows 10 1903/1909".to_string(),
                17763 => "Windows 10 1809/Server 2019".to_string(),
                17134 => "Windows 10 1803".to_string(),
                16299 => "Windows 10 1709".to_string(),
                15063 => "Windows 10 1703".to_string(),
                14393 => "Windows 10 1607/Server 2016".to_string(),
                10586 => "Windows 10 1511".to_string(),
                10240 => "Windows 10 RTM".to_string(),
                _ if build >= 22000 => "Windows 11".to_string(),
                _ if build >= 10240 => "Windows 10".to_string(),
                _ => format!("Windows (构建号: {})", build),
            };

            ((10, 0), corrected_name, true)
        } else {
            // 使用原始检测结果
            let version_name = match (major, minor) {
                (10, 0) => {
                    match build {
                        22000.. => "Windows 11".to_string(),
                        20348..=22000 => "Windows Server 2022".to_string(),
                        17763 => "Windows Server 2019".to_string(),
                        14393 => "Windows Server 2016".to_string(),
                        _ => "Windows 10".to_string(),
                    }
                },
                (6, 3) => "Windows 8.1".to_string(),
                (6, 2) => "Windows 8".to_string(),
                (6, 1) => "Windows 7".to_string(),
                (6, 0) => "Windows Vista".to_string(),
                (5, 2) => "Windows XP x64/Server 2003".to_string(),
                (5, 1) => "Windows XP".to_string(),
                _ => format!("Unknown Windows {}.{}", major, minor),
            };

            let supports_uiaccess = major > 6 || (major == 6 && minor >= 2);
            ((major, minor), version_name, supports_uiaccess)
        };

        let final_major = corrected_version.0;
        let final_minor = corrected_version.1;

        println!("🔍 最终检测结果: {} (版本号: {}.{}, 构建号: {})",
            version_name, final_major, final_minor, build);

        WindowsVersion {
            major: final_major,
            minor: final_minor,
            build,
            version_name,
            supports_uiaccess,
        }
    }
}

/// 检测Windows版本是否支持UIAccess
pub fn is_uiaccess_supported() -> bool {
    let version = get_windows_version();

    if version.supports_uiaccess {
        println!("✅ 当前系统支持UIAccess功能");
    } else {
        println!("⚠️ 当前系统不支持UIAccess功能，将使用标准置顶权限");
    }

    version.supports_uiaccess
}

/// 权限级别枚举
#[derive(Debug, Clone, PartialEq)]
pub enum PrivilegeLevel {
    /// 标准权限 - 基本的窗口置顶
    Standard,
    /// 高级置顶权限 - TOPMOST窗口
    TopMost,
    /// UIAccess权限 - 最高级别的系统访问权限
    UIAccess,
}

impl PrivilegeLevel {
    pub fn description(&self) -> &str {
        match self {
            PrivilegeLevel::Standard => "标准权限",
            PrivilegeLevel::TopMost => "高级置顶权限",
            PrivilegeLevel::UIAccess => "UIAccess系统权限",
        }
    }
}

/// 权限管理结果
#[derive(Debug, Clone)]
pub struct PrivilegeResult {
    pub level: PrivilegeLevel,
    pub success: bool,
    pub message: String,
    pub windows_version: WindowsVersion,
}

/// 动态权限管理 - 根据Windows版本自动选择最佳权限策略
pub fn prepare_optimal_privileges() -> Result<PrivilegeResult> {
    let version = get_windows_version();

    println!("🔧 开始权限管理流程...");

    if version.supports_uiaccess {
        // Windows 8+ 系统，尝试获取UIAccess权限
        println!("🎯 目标权限级别: UIAccess (适用于 {})", version.version_name);

        match attempt_uiaccess_privileges() {
            Ok(true) => {
                let result = PrivilegeResult {
                    level: PrivilegeLevel::UIAccess,
                    success: true,
                    message: format!("✅ 成功获取UIAccess权限 ({})", version.version_name),
                    windows_version: version,
                };
                println!("{}", result.message);
                Ok(result)
            }
            Ok(false) | Err(_) => {
                // UIAccess获取失败，降级到TopMost权限
                println!("⚠️ UIAccess权限获取失败，降级到高级置顶权限");
                apply_topmost_privileges(&version)
            }
        }
    } else {
        // Windows 7 或更早版本，直接使用TopMost权限
        println!("🎯 目标权限级别: TopMost (适用于 {})", version.version_name);
        apply_topmost_privileges(&version)
    }
}

/// 尝试获取UIAccess权限 (Windows 8+)
fn attempt_uiaccess_privileges() -> Result<bool> {
    // Check if we already have UIAccess
    match privilege::check_for_ui_access() {
        Ok(true) => {
            println!("✅ 已具有UIAccess权限");
            Ok(true)
        }
        Ok(false) => {
            println!("🔄 尝试获取UIAccess权限...");
            // Need to get UIAccess
            let ui_access_token = token::create_ui_access_token()?;
            process::restart_with_token(ui_access_token)?;
            // If we get here, the restart failed
            Ok(false)
        }
        Err(e) => {
            println!("❌ UIAccess检查失败: {}", e);
            Err(e)
        }
    }
}

/// 应用TopMost权限 (Windows 7 及 Windows 8+ 降级方案)
fn apply_topmost_privileges(version: &WindowsVersion) -> Result<PrivilegeResult> {
    println!("🔧 应用高级置顶权限...");

    // 对于TopMost权限，我们不需要特殊的令牌操作
    // 这将在窗口创建时通过SetWindowPos设置HWND_TOPMOST来实现

    let result = PrivilegeResult {
        level: PrivilegeLevel::TopMost,
        success: true,
        message: format!("✅ 成功应用高级置顶权限 ({})", version.version_name),
        windows_version: version.clone(),
    };

    println!("{}", result.message);
    println!("ℹ️ 窗口将具有最高置顶优先级，可以覆盖大多数其他窗口");

    Ok(result)
}

// 保持向后兼容的函数
pub fn prepare_for_ui_access() -> Result<bool> {
    match prepare_optimal_privileges() {
        Ok(result) => Ok(result.level == PrivilegeLevel::UIAccess),
        Err(_) => Ok(false),
    }
}
