// token.rs - Token manipulation functions (Windows 7兼容版本)
use crate::{Result, UiAccessError, is_uiaccess_supported};
use windows::{
    Win32::{
        Foundation::{CloseHandle, BOOL, HANDLE, TRUE},
        Security::{
            DuplicateTokenEx, GetTokenInformation, SetTokenInformation,
            TokenPrimary, TokenSessionId, TokenUIAccess,
            TOKEN_QUERY, TOKEN_DUPLICATE, TOKEN_ASSIGN_PRIMARY, TOKEN_ADJUST_DEFAULT,
            SecurityAnonymous,
        },
        System::Threading::{
            GetCurrentProcess, SetThreadToken, OpenProcessToken,
        },

    },
};

// Create a token with UIAccess privileges (Windows 7兼容版本)
pub fn create_ui_access_token() -> Result<HANDLE> {
    // 首先检查系统是否支持UIAccess
    if !is_uiaccess_supported() {
        println!("⚠️ 系统不支持UIAccess，无法创建UIAccess令牌");
        return Err(UiAccessError::Other("UIAccess not supported on this system".to_string()));
    }
    // Open current process token
    let mut token_self = HANDLE::default();
    let result = unsafe {
        OpenProcessToken(
            GetCurrentProcess(),
            TOKEN_QUERY | TOKEN_DUPLICATE,
            &mut token_self,
        )
    };

    if !result.as_bool() {
        return Err(UiAccessError::TokenError(windows::core::Error::from_win32().code().0 as u32));
    }

    // Get current session ID
    let mut session_id: u32 = 0;
    let mut return_length: u32 = 0;
    let result = unsafe {
        GetTokenInformation(
            token_self,
            TokenSessionId,
            Some(&mut session_id as *mut u32 as *mut _),
            std::mem::size_of::<u32>() as u32,
            &mut return_length,
        )
    };

    if !result.as_bool() {
        unsafe { CloseHandle(token_self); }
        return Err(UiAccessError::TokenError(windows::core::Error::from_win32().code().0 as u32));
    }

    // Get system token from winlogon.exe
    let token_system: HANDLE = match crate::process::duplicate_winlogon_token(session_id) {
        Ok(token) => token,
        Err(e) => {
            unsafe { CloseHandle(token_self); }
            return Err(e);
        }
    };

    // Impersonate system token
    let result = unsafe { SetThreadToken(None, token_system) };
    if !result.as_bool() {
        let error = windows::core::Error::from_win32().code().0 as u32;
        unsafe {
            CloseHandle(token_system);
            CloseHandle(token_self);
        }
        return Err(UiAccessError::TokenError(error));
    }

    // Create new token with UIAccess
    let mut new_token = HANDLE::default();
    let result = unsafe {
        DuplicateTokenEx(
            token_self,
            TOKEN_QUERY | TOKEN_DUPLICATE | TOKEN_ASSIGN_PRIMARY | TOKEN_ADJUST_DEFAULT,
            None, // Security attributes
            SecurityAnonymous,  // 正确使用SECURITY_IMPERSONATION_LEVEL枚举值
            TokenPrimary,
            &mut new_token,
        )
    };

    if !result.as_bool() {
        let error = windows::core::Error::from_win32().code().0 as u32;
        unsafe {
            SetThreadToken(None, HANDLE::default()); // Revert to self
            CloseHandle(token_system);
            CloseHandle(token_self);
        }
        return Err(UiAccessError::TokenError(error));
    }

    // Set UIAccess flag
    let ui_access: BOOL = TRUE;
    let result = unsafe {
        SetTokenInformation(
            new_token,
            TokenUIAccess,
            &ui_access as *const BOOL as *const _,
            std::mem::size_of::<BOOL>() as u32,
        )
    };

    if !result.as_bool() {
        let error = windows::core::Error::from_win32().code().0 as u32;
        unsafe {
            CloseHandle(new_token);
            SetThreadToken(None, HANDLE::default()); // Revert to self
            CloseHandle(token_system);
            CloseHandle(token_self);
        }
        return Err(UiAccessError::TokenError(error));
    }

    // Revert impersonation and clean up
    unsafe {
        SetThreadToken(None, HANDLE::default()); // Revert to self
        CloseHandle(token_system);
        CloseHandle(token_self);
    }

    Ok(new_token)
}
