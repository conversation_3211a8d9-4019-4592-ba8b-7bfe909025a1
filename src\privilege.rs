// privilege.rs - Privilege checking functions (Windows 7兼容版本)
use crate::{Result, UiAccessError, is_uiaccess_supported};
use windows::{
    Win32::{
        Foundation::{CloseHandle, BOOL, HANDLE},
        Security::{
            GetTokenInformation, TokenUIAccess, TOKEN_QUERY
        },
        System::Threading::{GetCurrentProcess, OpenProcessToken},
    },
};

// Check if current process has UIAccess privilege (Windows 7兼容版本)
pub fn check_for_ui_access() -> Result<bool> {
    // 首先检查系统是否支持UIAccess
    if !is_uiaccess_supported() {
        println!("ℹ️ 系统不支持UIAccess，跳过检查");
        return Ok(false);
    }

    let mut token = HANDLE::default();
    let result = unsafe {
        OpenProcessToken(
            GetCurrentProcess(),
            TOKEN_QUERY,
            &mut token,
        )
    };

    if !result.as_bool() {
        let error_code = windows::core::Error::from_win32().code().0 as u32;
        println!("⚠️ 无法打开进程令牌: 0x{:x}", error_code);
        return Err(UiAccessError::TokenError(error_code));
    }

    let mut ui_access: BOOL = BOOL::default();
    let mut return_length: u32 = 0;
    let result = unsafe {
        GetTokenInformation(
            token,
            TokenUIAccess,
            Some(&mut ui_access as *mut BOOL as *mut _),
            std::mem::size_of::<BOOL>() as u32,
            &mut return_length,
        )
    };

    unsafe { CloseHandle(token); }

    if !result.as_bool() {
        let error_code = windows::core::Error::from_win32().code().0 as u32;
        println!("⚠️ 无法获取UIAccess信息: 0x{:x}", error_code);
        // 在Windows 7上，这个API调用可能失败，这是正常的
        if !is_uiaccess_supported() {
            println!("ℹ️ 这在Windows 7上是正常的，UIAccess不受支持");
            return Ok(false);
        }
        return Err(UiAccessError::TokenError(error_code));
    }

    Ok(ui_access.as_bool())
}
