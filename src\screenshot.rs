use windows::{
    core::*,
    Win32::{
        Foundation::{HWND, WPA<PERSON><PERSON>, LPARAM, LRESULT, RECT, POINT, COLORREF, GetLastError, ERROR_CLASS_ALREADY_EXISTS, TRUE},
        Graphics::Gdi::{
            <PERSON>gin<PERSON>aint, EndPaint, CreateSolidBrush, FillRect, DeleteObject, DeleteDC,
            CreatePen, SelectObject, MoveToEx, LineTo, PS_SOLID, HBITMAP,
            GetDC, ReleaseDC, CreateCompatibleDC, CreateCompatibleBitmap,
            BitBlt, SRCCOPY, GetDIBits, DIB_RGB_COLORS, BITMAPINFO,
            BITMAPINFOHEADER, BI_RGB, PAINTSTRUCT, InvalidateRect,
        },
        UI::WindowsAndMessaging::{
            CreateWindowExW, RegisterClassW, DestroyWindow, DefWindowProcW,
            GetClientRect, ShowWindow, SW_HIDE,
            WS_EX_TOPMOST, WS_EX_LAYERED, WS_EX_TOOLWINDOW, WS_POPUP, WS_VISIBLE,
            WM_PAINT, WM_LBUTTONDOWN, WM_MOUSEMOVE, WM_LBUTTONUP, WM_KEYDOWN, WM_DESTROY,
            WNDCLASSW, LoadCursorW, IDC_CROSS, GetSystemMetrics, SM_CXSCREEN, SM_CYSCREEN,
            SetLayeredWindowAttributes, LWA_ALPHA, LWA_COLORKEY,
        },
        UI::Input::KeyboardAndMouse::VK_ESCAPE,
        System::LibraryLoader::GetModuleHandleW,

    },
};
use base64::{Engine as _, engine::general_purpose};

// 截图状态
#[derive(Debug, Clone)]
pub struct ScreenshotArea {
    pub x: i32,
    pub y: i32,
    pub width: i32,
    pub height: i32,
}

// 截图结果
#[derive(Debug)]
pub struct ScreenshotResult {
    pub base64_data: String,
    pub area: ScreenshotArea,
}

/// 轻量级截图功能
pub struct ScreenCapture {
    overlay_hwnd: Option<HWND>,
    is_selecting: bool,
    start_point: POINT,
    end_point: POINT,
}

// 简化的全局状态
static mut IS_SELECTING: bool = false;
static mut START_X: i32 = 0;
static mut START_Y: i32 = 0;
static mut END_X: i32 = 0;
static mut END_Y: i32 = 0;
static mut OVERLAY_HWND: Option<HWND> = None;

impl ScreenCapture {
    pub fn new() -> Self {
        Self {
            overlay_hwnd: None,
            is_selecting: false,
            start_point: POINT { x: 0, y: 0 },
            end_point: POINT { x: 0, y: 0 },
        }
    }

    /// 开始区域截图
    pub fn start_area_capture(&mut self) -> std::result::Result<(), String> {
        println!("📸 开始区域截图...");

        unsafe {
            // 重置全局状态
            IS_SELECTING = false;
            START_X = 0;
            START_Y = 0;
            END_X = 0;
            END_Y = 0;
        }

        // 创建全屏遮罩窗口
        self.create_overlay_window()?;

        Ok(())
    }

    /// 创建全屏遮罩窗口
    fn create_overlay_window(&mut self) -> std::result::Result<(), String> {
        unsafe {
            // 获取屏幕尺寸
            let screen_width = GetSystemMetrics(SM_CXSCREEN);
            let screen_height = GetSystemMetrics(SM_CYSCREEN);

            // 注册窗口类
            let class_name = w!("ScreenshotOverlay");
            let wc = WNDCLASSW {
                lpfnWndProc: Some(overlay_window_proc),
                hInstance: GetModuleHandleW(None).unwrap(),
                lpszClassName: class_name,
                hCursor: LoadCursorW(None, IDC_CROSS).unwrap(),
                hbrBackground: CreateSolidBrush(COLORREF(0x00000001)), // 几乎透明
                ..Default::default()
            };

            if RegisterClassW(&wc) == 0 {
                let error = GetLastError();
                if error.0 != ERROR_CLASS_ALREADY_EXISTS.0 {
                    return Err(format!("注册窗口类失败: {:?}", error));
                }
            }

            // 创建全屏遮罩窗口
            let hwnd = CreateWindowExW(
                WS_EX_TOPMOST | WS_EX_LAYERED | WS_EX_TOOLWINDOW,
                class_name,
                w!("Screenshot Overlay"),
                WS_POPUP | WS_VISIBLE,
                0, 0, screen_width, screen_height,
                None, None,
                GetModuleHandleW(None).unwrap(),
                None, // 不传递用户数据，使用全局变量
            );

            if hwnd.0 == 0 {
                return Err("创建遮罩窗口失败".to_string());
            }

            // 设置窗口透明度 - 使用很低的alpha值，减少黑色覆盖
            SetLayeredWindowAttributes(hwnd, COLORREF(0), 10, LWA_ALPHA).ok();

            self.overlay_hwnd = Some(hwnd);
            OVERLAY_HWND = Some(hwnd); // 记录全局句柄
            println!("✅ 遮罩窗口创建成功，HWND: {:?}", hwnd);
        }

        Ok(())
    }

    /// 处理鼠标按下
    pub fn handle_mouse_down(&mut self, x: i32, y: i32) {
        self.is_selecting = true;
        self.start_point = POINT { x, y };
        self.end_point = POINT { x, y };
        println!("🖱️ 开始选择区域: ({}, {})", x, y);
    }

    /// 处理鼠标移动
    pub fn handle_mouse_move(&mut self, x: i32, y: i32) {
        if self.is_selecting {
            self.end_point = POINT { x, y };
            // 重绘遮罩窗口显示选择框
            if let Some(hwnd) = self.overlay_hwnd {
                unsafe {
                    InvalidateRect(hwnd, None, TRUE);
                }
            }
        }
    }

    /// 处理鼠标释放
    pub fn handle_mouse_up(&mut self, x: i32, y: i32) -> Option<ScreenshotArea> {
        println!("🔍 Debug: handle_mouse_up调用，is_selecting={}, 坐标=({}, {})", self.is_selecting, x, y);

        // 先保存选择状态，因为可能在其他地方被重置了
        let was_selecting = self.is_selecting;

        if !was_selecting {
            println!("⚠️ Debug: 不在选择状态，忽略mouse_up");
            return None;
        }

        // 现在设置为false
        self.is_selecting = false;
        self.end_point = POINT { x, y };

        println!("🔍 Debug: 起始点=({}, {}), 结束点=({}, {})",
            self.start_point.x, self.start_point.y, self.end_point.x, self.end_point.y);

        // 计算选择区域
        let left = self.start_point.x.min(self.end_point.x);
        let top = self.start_point.y.min(self.end_point.y);
        let right = self.start_point.x.max(self.end_point.x);
        let bottom = self.start_point.y.max(self.end_point.y);

        let width = right - left;
        let height = bottom - top;

        println!("🔍 Debug: 计算区域 left={}, top={}, width={}, height={}", left, top, width, height);

        // 检查最小尺寸
        if width < 10 || height < 10 {
            println!("⚠️ 选择区域太小，忽略 ({}x{})", width, height);
            return None;
        }

        let area = ScreenshotArea {
            x: left,
            y: top,
            width,
            height,
        };

        println!("📸 选择区域完成: {:?}", area);

        // 不在这里关闭遮罩窗口，让WM_LBUTTONUP处理器来关闭

        Some(area)
    }

    /// 关闭遮罩窗口
    fn close_overlay(&mut self) {
        if let Some(hwnd) = self.overlay_hwnd.take() {
            unsafe {
                DestroyWindow(hwnd).ok();
            }
            println!("🔒 遮罩窗口已关闭");
        }
    }

    /// 截取指定区域
    pub fn capture_area(&self, area: &ScreenshotArea) -> std::result::Result<ScreenshotResult, String> {
        println!("📸 开始截取区域: {:?}", area);

        unsafe {
            // 获取屏幕DC
            let screen_dc = GetDC(None);
            if screen_dc.0 == 0 {
                return Err("获取屏幕DC失败".to_string());
            }

            // 暂时不处理DPI缩放，先修复图像旋转问题

            // 创建兼容DC
            let mem_dc = CreateCompatibleDC(screen_dc);
            if mem_dc.0 == 0 {
                ReleaseDC(None, screen_dc);
                return Err("创建兼容DC失败".to_string());
            }

            // 创建位图（使用原始尺寸）
            let bitmap = CreateCompatibleBitmap(screen_dc, area.width, area.height);
            if bitmap.0 == 0 {
                DeleteDC(mem_dc);
                ReleaseDC(None, screen_dc);
                return Err("创建位图失败".to_string());
            }

            // 选择位图到DC
            let old_bitmap = SelectObject(mem_dc, bitmap);

            // 复制屏幕内容到位图
            let result = BitBlt(
                mem_dc,
                0, 0, area.width, area.height,
                screen_dc,
                area.x, area.y,
                SRCCOPY,
            );

            if !result.as_bool() {
                SelectObject(mem_dc, old_bitmap);
                DeleteObject(bitmap);
                DeleteDC(mem_dc);
                ReleaseDC(None, screen_dc);
                return Err("复制屏幕内容失败".to_string());
            }

            // 调试功能已移除以减小包体

            // 转换为PNG格式的base64
            let base64_data = self.bitmap_to_png_base64(bitmap, area.width, area.height)?;

            // 清理资源
            SelectObject(mem_dc, old_bitmap);
            DeleteObject(bitmap);
            DeleteDC(mem_dc);
            ReleaseDC(None, screen_dc);

            println!("✅ 截图完成，数据长度: {} 字节", base64_data.len());

            Ok(ScreenshotResult {
                base64_data,
                area: area.clone(),
            })
        }
    }



    /// 将位图转换为PNG格式的base64
    fn bitmap_to_png_base64(&self, bitmap: HBITMAP, width: i32, height: i32) -> std::result::Result<String, String> {
        unsafe {
            // 获取位图信息
            let mut bitmap_info = BITMAPINFO {
                bmiHeader: BITMAPINFOHEADER {
                    biSize: std::mem::size_of::<BITMAPINFOHEADER>() as u32,
                    biWidth: width,
                    biHeight: -height, // 负值，从上到下扫描
                    biPlanes: 1,
                    biBitCount: 24, // 24位RGB
                    biCompression: BI_RGB.0 as u32,
                    ..Default::default()
                },
                ..Default::default()
            };

            // 计算数据大小
            let bytes_per_line = ((width * 3 + 3) / 4) * 4; // 4字节对齐
            let data_size = bytes_per_line * height;

            // 分配内存
            let mut buffer = vec![0u8; data_size as usize];

            // 获取屏幕DC
            let screen_dc = GetDC(None);

            // 获取位图数据
            let result = GetDIBits(
                screen_dc,
                bitmap,
                0,
                height as u32,
                Some(buffer.as_mut_ptr() as *mut _),
                &mut bitmap_info,
                DIB_RGB_COLORS,
            );

            ReleaseDC(None, screen_dc);

            if result == 0 {
                return Err("获取位图数据失败".to_string());
            }

            // 转换BGR到RGB
            let mut rgb_data = Vec::with_capacity((width * height * 3) as usize);

            for y in 0..height {
                let row_start = (y * bytes_per_line) as usize;
                for x in 0..width {
                    let pixel_start = row_start + (x * 3) as usize;
                    if pixel_start + 2 < buffer.len() {
                        // Windows位图是BGR格式，需要转换为RGB
                        let b = buffer[pixel_start];
                        let g = buffer[pixel_start + 1];
                        let r = buffer[pixel_start + 2];
                        rgb_data.push(r);
                        rgb_data.push(g);
                        rgb_data.push(b);
                    }
                }
            }

            // 使用png库编码
            let mut png_data = Vec::new();
            {
                let mut encoder = png::Encoder::new(std::io::Cursor::new(&mut png_data), width as u32, height as u32);
                encoder.set_color(png::ColorType::Rgb);
                encoder.set_depth(png::BitDepth::Eight);

                let mut writer = encoder.write_header().map_err(|e| format!("PNG头写入失败: {}", e))?;
                writer.write_image_data(&rgb_data).map_err(|e| format!("PNG数据写入失败: {}", e))?;
            }

            // 转换为base64
            let base64_data = general_purpose::STANDARD.encode(&png_data);

            println!("✅ PNG转换完成，PNG大小: {} 字节，base64长度: {}", png_data.len(), base64_data.len());

            Ok(base64_data)
        }
    }

    /// 快速全屏截图
    pub fn capture_fullscreen() -> std::result::Result<ScreenshotResult, String> {
        unsafe {
            let screen_width = GetSystemMetrics(SM_CXSCREEN);
            let screen_height = GetSystemMetrics(SM_CYSCREEN);

            let area = ScreenshotArea {
                x: 0,
                y: 0,
                width: screen_width,
                height: screen_height,
            };

            let capture = ScreenCapture::new();
            capture.capture_area(&area)
        }
    }
}

/// 遮罩窗口过程
unsafe extern "system" fn overlay_window_proc(
    hwnd: HWND,
    msg: u32,
    wparam: WPARAM,
    lparam: LPARAM,
) -> LRESULT {
    match msg {
        WM_PAINT => {
            let mut ps = PAINTSTRUCT::default();
            let hdc = BeginPaint(hwnd, &mut ps);

            // 绘制半透明背景
            let mut rect = RECT::default();
            let _ = GetClientRect(hwnd, &mut rect);

            // 创建很淡的白色背景，让黑色选择框可见
            let brush = CreateSolidBrush(COLORREF(0x00FFFFFF)); // 白色背景
            FillRect(hdc, &rect, brush);
            DeleteObject(brush);

            // 如果正在选择，绘制选择框
            if IS_SELECTING {
                let start_x = START_X.min(END_X);
                let start_y = START_Y.min(END_Y);
                let width = (START_X - END_X).abs();
                let height = (START_Y - END_Y).abs();

                // 绘制选择框边框 - 使用纯黑色，在白色生产环境中醒目
                let pen = CreatePen(PS_SOLID, 3, COLORREF(0x00000000)); // 纯黑色边框，3像素宽
                let old_pen = SelectObject(hdc, pen);

                // 绘制矩形框
                MoveToEx(hdc, start_x, start_y, None);
                LineTo(hdc, start_x + width, start_y);
                LineTo(hdc, start_x + width, start_y + height);
                LineTo(hdc, start_x, start_y + height);
                LineTo(hdc, start_x, start_y);

                SelectObject(hdc, old_pen);
                DeleteObject(pen);
            }

            EndPaint(hwnd, &ps);
            LRESULT(0)
        }
        WM_LBUTTONDOWN => {
            let x = (lparam.0 & 0xFFFF) as i16 as i32;
            let y = ((lparam.0 >> 16) & 0xFFFF) as i16 as i32;

            println!("🖱️ 开始选择区域: ({}, {})", x, y);

            // 直接设置全局状态
            IS_SELECTING = true;
            START_X = x;
            START_Y = y;
            END_X = x;
            END_Y = y;

            LRESULT(0)
        }
        WM_MOUSEMOVE => {
            let x = (lparam.0 & 0xFFFF) as i16 as i32;
            let y = ((lparam.0 >> 16) & 0xFFFF) as i16 as i32;

            // 直接处理鼠标移动
            if IS_SELECTING {
                END_X = x;
                END_Y = y;
                InvalidateRect(hwnd, None, TRUE); // 重绘窗口
            }

            LRESULT(0)
        }
        WM_LBUTTONUP => {
            let x = (lparam.0 & 0xFFFF) as i16 as i32;
            let y = ((lparam.0 >> 16) & 0xFFFF) as i16 as i32;

            println!("🖱️ 完成选择区域: ({}, {})", x, y);

            if IS_SELECTING {
                IS_SELECTING = false;
                END_X = x;
                END_Y = y;

                // 计算选择区域
                let left = START_X.min(END_X);
                let top = START_Y.min(END_Y);
                let right = START_X.max(END_X);
                let bottom = START_Y.max(END_Y);

                let width = right - left;
                let height = bottom - top;

                println!("🔍 Debug: 计算区域 left={}, top={}, width={}, height={}", left, top, width, height);

                // 检查最小尺寸
                if width >= 10 && height >= 10 {
                    let area = ScreenshotArea {
                        x: left,
                        y: top,
                        width,
                        height,
                    };

                    println!("📸 选择区域完成: {:?}", area);

                    // 执行区域截图
                    let capture = ScreenCapture::new();
                    match capture.capture_area(&area) {
                        Ok(result) => {
                            println!("✅ 区域截图成功，数据长度: {}", result.base64_data.len());
                            println!("🐛 Debug: 截图区域 x={}, y={}, w={}, h={}",
                                area.x, area.y, area.width, area.height);

                            // 保存截图数据
                            crate::simple_test::save_screenshot_data_internal(result.base64_data);

                            println!("💾 截图数据已保存，可以使用ALT+S发送");

                            // 截图完成后自动显示主窗口
                            crate::simple_test::show_main_window();

                            // 立即强制销毁遮罩窗口
                            println!("🔒 截图完成，立即销毁遮罩窗口...");
                            let destroy_result = DestroyWindow(hwnd);
                            if destroy_result.as_bool() {
                                println!("✅ 遮罩窗口立即销毁成功");
                            } else {
                                println!("❌ 遮罩窗口立即销毁失败，强制隐藏");
                                ShowWindow(hwnd, SW_HIDE);
                            }
                        }
                        Err(e) => {
                            println!("❌ 区域截图失败: {}", e);

                            // 截图失败也要销毁遮罩窗口
                            println!("🔒 截图失败，销毁遮罩窗口...");
                            let destroy_result = DestroyWindow(hwnd);
                            if destroy_result.as_bool() {
                                println!("✅ 遮罩窗口销毁成功(失败情况)");
                            } else {
                                println!("❌ 遮罩窗口销毁失败(失败情况)，强制隐藏");
                                ShowWindow(hwnd, SW_HIDE);
                            }
                        }
                    }
                } else {
                    println!("⚠️ 选择区域太小，忽略 ({}x{})", width, height);

                    // 区域太小也要销毁遮罩窗口
                    println!("🔒 区域太小，销毁遮罩窗口...");
                    let destroy_result = DestroyWindow(hwnd);
                    if destroy_result.as_bool() {
                        println!("✅ 遮罩窗口销毁成功(区域太小)");
                    } else {
                        println!("❌ 遮罩窗口销毁失败(区域太小)，强制隐藏");
                        ShowWindow(hwnd, SW_HIDE);
                    }
                }
            }

            // 关闭遮罩窗口
            println!("🔒 正在销毁截图遮罩窗口...");
            let destroy_result = DestroyWindow(hwnd);
            if destroy_result.as_bool() {
                println!("✅ 截图遮罩窗口销毁成功");
            } else {
                println!("❌ 截图遮罩窗口销毁失败");
                // 强制隐藏窗口
                ShowWindow(hwnd, SW_HIDE);
                println!("🔒 已强制隐藏遮罩窗口");
            }

            LRESULT(0)
        }
        WM_KEYDOWN => {
            if wparam.0 == VK_ESCAPE.0 as usize {
                println!("⌨️ 用户取消截图");
                // ESC键取消截图，恢复主窗口显示
                crate::simple_test::show_main_window();

                println!("🔒 正在销毁截图遮罩窗口(ESC)...");
                let destroy_result = DestroyWindow(hwnd);
                if destroy_result.as_bool() {
                    println!("✅ 截图遮罩窗口销毁成功(ESC)");
                } else {
                    println!("❌ 截图遮罩窗口销毁失败(ESC)");
                    // 强制隐藏窗口
                    ShowWindow(hwnd, SW_HIDE);
                    println!("🔒 已强制隐藏遮罩窗口(ESC)");
                }
            }
            LRESULT(0)
        }
        WM_DESTROY => {
            // 清理全局状态
            IS_SELECTING = false;
            OVERLAY_HWND = None; // 清除全局句柄
            println!("🔒 遮罩窗口已销毁，清理全局状态");
            LRESULT(0)
        }
        _ => DefWindowProcW(hwnd, msg, wparam, lparam),
    }
}

/// 获取当前的遮罩窗口句柄（用于强制清理）
pub fn get_overlay_hwnd() -> Option<HWND> {
    unsafe { OVERLAY_HWND }
}

/// 强制清理遮罩窗口
pub fn force_cleanup_overlay() {
    unsafe {
        if let Some(hwnd) = OVERLAY_HWND {
            println!("🔒 强制清理遮罩窗口: {:?}", hwnd);

            // 先隐藏
            ShowWindow(hwnd, SW_HIDE);

            // 再销毁
            let destroy_result = DestroyWindow(hwnd);
            if destroy_result.as_bool() {
                println!("✅ 强制清理遮罩窗口成功");
            } else {
                println!("❌ 强制清理遮罩窗口失败");
            }

            // 清除全局状态
            OVERLAY_HWND = None;
            IS_SELECTING = false;
        } else {
            println!("ℹ️ 没有活动的遮罩窗口需要清理");
        }
    }
}
