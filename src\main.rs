// main.rs - Main application entry point

// 设置为 Windows 子系统，不显示控制台窗口
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use uiaccess_lib::{prepare_for_ui_access, create_simple_test_window, is_uiaccess_supported};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 启动 Rust UIAccess 透明窗口应用 (Windows 7兼容版本)");

    // 检查系统兼容性
    if is_uiaccess_supported() {
        println!("ℹ️ 检测到支持UIAccess的系统 (Windows 8+)");
    } else {
        println!("ℹ️ 检测到Windows 7系统，UIAccess功能将被禁用");
        println!("ℹ️ 应用将以标准权限运行，大部分功能仍然可用");
    }

    // 准备UIAccess权限（Windows 7兼容）
    match prepare_for_ui_access() {
        Ok(has_ui_access) => {
            if has_ui_access {
                println!("✅ 成功获取UIAccess权限");
            } else {
                if is_uiaccess_supported() {
                    println!("⚠️ 无法获取UIAccess权限，但没有错误发生");
                    println!("💡 提示：可能需要以管理员身份运行或者程序需要数字签名");
                } else {
                    println!("ℹ️ Windows 7系统，以标准权限运行");
                }
                // 即使没有UIAccess权限，我们仍然可以创建窗口，只是可能无法置顶
            }
        }
        Err(e) => {
            println!("❌ 准备UIAccess时出错: {}", e);
            if is_uiaccess_supported() {
                println!("💡 提示：将继续运行，但可能无法获得完整的UIAccess功能");
            } else {
                println!("💡 提示：这在Windows 7上是正常的，将继续运行");
            }
            // 不直接返回错误，让应用继续运行
        }
    }

    println!("🎨 使用简单测试版本创建透明窗口应用");

    // 使用简单测试版本创建透明窗口应用
    create_simple_test_window()?;

    println!("👋 应用已退出");
    Ok(())
}
