# 安全AI代理服务开发对接文档

## 1. 项目概述

本项目是一个安全的AI代理服务，提供加密通信机制，支持文本和图像输入，连接到Deepseek等AI服务。主要特点是所有客户端和服务器之间的通信都经过加密处理，确保数据传输安全。

## 2. 系统架构

系统主要由以下几个部分组成：

- **配置管理**：负责加载和管理系统配置
- **加密服务**：处理数据加密和解密
- **API层**：提供HTTP接口
- **AI服务**：连接到Deepseek等AI服务
- **OCR服务**：使用阿里云OCR API处理图像识别

## 3. 配置说明

配置文件位于 `config/config.yaml`，主要包含以下配置项：

```yaml
server:
  port: 8080              # 服务器监听端口
  timeout: 60s            # 请求超时时间
  debug: true             # 是否开启调试模式

security:
  encryption_key: "..."   # 加密密钥，32字节
  encryption_iv: "..."    # 加密IV值，16字节
  enable_https: false     # 是否启用HTTPS
  cert_file: "..."        # 证书文件路径
  key_file: "..."         # 密钥文件路径

ai:
  api_url: "..."          # AI服务API地址
  api_key: "..."          # AI服务API密钥
  model: "..."            # 使用的模型名称
  system_prompt: "..."    # 系统提示词
  timeout: 30s            # AI请求超时时间

ocr:
  timeout: 30s                                    # OCR处理超时时间
  aliyun_access_key_id: "..."                    # 阿里云AccessKey ID
  aliyun_access_key_secret: "..."                # 阿里云AccessKey Secret
  aliyun_endpoint: "ocr-api.cn-hangzhou.aliyuncs.com"  # 阿里云OCR API端点
  ocr_type: "Advanced"                           # OCR识别类型（Advanced/General等）
```

## 4. 阿里云OCR配置

### 4.1 阿里云OCR服务配置

本项目使用阿里云OCR统一识别API进行图像文字识别。需要在阿里云控制台开通OCR服务并获取访问凭证。

### 4.2 配置说明

OCR相关配置在 `config/config.yaml` 的 `ocr` 部分：

```yaml
ocr:
  timeout: 30s                                    # OCR处理超时时间
  aliyun_access_key_id: "123456"     # 阿里云AccessKey ID
  aliyun_access_key_secret: "123456"  # 阿里云AccessKey Secret
  aliyun_endpoint: "ocr-api.cn-hangzhou.aliyuncs.com"  # 阿里云OCR API端点
  ocr_type: "Advanced"                           # OCR识别类型
```

### 4.4 支持的OCR类型

- `Advanced`：通用文字识别高精版（默认）
- `General`：通用文字识别基础版
- 更多类型请参考阿里云OCR API文档

## 5. 加密机制详解

### 5.1 加密算法

系统使用AES-GCM加密算法，具有以下特点：

- AES-256加密：使用256位密钥
- GCM模式：提供认证加密，确保数据完整性和机密性
- 固定IV值：使用配置文件中指定的IV值，而非随机生成

### 5.2 加密配置

加密相关的配置项在 `config/config.yaml` 的 `security` 部分：

```yaml
security:
  encryption_key: "key"  # 32字节的加密密钥
  encryption_iv: "iv"                  # 固定的IV值（16字节）
```

**重要说明**：
- `encryption_key` 必须是至少32字节长的字符串
- `encryption_iv` 必须是至少12字节长的字符串，系统将使用前12个字节作为nonce

### 5.3 加密过程

1. 创建AES-256密码块
2. 使用GCM模式
3. 使用固定的IV值（从配置文件读取）
4. 加密明文数据
5. 将加密后的数据进行Base64编码

### 5.4 解密过程

1. Base64解码密文
2. 提取nonce部分（前12字节）
3. 使用相同的密钥和nonce进行GCM解密
4. 返回明文

## 6. API接口说明

### 6.1 基本通信流程

1. 客户端使用共享的加密密钥和IV值加密请求内容
2. 服务器接收加密内容，解密后处理请求
3. 服务器加密响应内容，返回给客户端
4. 客户端解密响应内容

### 6.2 请求格式

```json
{
  "type": "text|image",  // 请求类型：文本或图像
  "content": "..."       // 加密后的内容：文本或Base64编码的图片
}
```

### 6.3 响应格式

```json
{
  "success": true,       // 是否成功
  "message": "...",      // 错误信息（仅当success为false时）
  "data": "..."          // 加密后的响应数据（仅当success为true时）
}
```

### 6.4 客户端下载

服务器提供了一个接口，用于下载客户端的二进制文件：

```http
GET /api/gc/:filename
```


## 7. 安全注意事项

1. **密钥管理**：确保加密密钥和IV值安全存储，可考虑使用环境变量传递
2. **通信安全**：生产环境建议启用HTTPS
3. **日志安全**：调试模式下会记录完整的明文和密文，生产环境应关闭
4. **固定IV**：虽然使用固定IV简化了实现，但在高安全要求场景下需考虑IV的随机性
5. **阿里云凭证安全**：阿里云AccessKey应通过环境变量配置，避免硬编码在配置文件中

## 8. 错误处理

常见错误及解决方案：

| 错误信息 | 可能原因 | 解决方案 |
|---------|---------|---------|
| 加密密钥长度不足 | 配置的密钥长度小于32字节 | 使用至少32字节长的密钥 |
| IV值长度不足 | 配置的IV值长度小于12字节 | 使用至少12字节长的IV值 |
| 密文长度不足 | 密文被截断或损坏 | 检查网络传输是否完整 |
| 解密失败 | 密钥或IV不匹配 | 确保客户端和服务器使用相同的密钥和IV |
| Aliyun OCR client is not initialized | 阿里云凭证配置错误 | 检查AccessKey ID和Secret配置 |
| OCR API调用失败 | 网络问题或API配额不足 | 检查网络连接和阿里云账户余额 |

## 9. 测试

建议进行以下测试：

1. 使用相同密钥和IV进行加解密测试
2. 验证长文本和二进制数据的加解密
3. 测试不同密钥和IV组合的错误情况
4. 性能测试（大量并发请求下的加解密性能）
5. OCR API测试（不同图片格式和内容）
6. OCR识别准确性测试

## 10. 扩展与定制

如需修改加密算法或OCR服务，可以在以下文件中进行更改：

- `utils/encryption.go`：加密核心实现
- `service/encryption.go`：加密服务封装
- `service/ocr_service.go`：阿里云OCR服务实现
- `config/config.go`：配置结构定义


