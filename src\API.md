# 安全AI代理服务开发对接文档

## 1. 项目概述

本项目是一个安全的AI代理服务，提供加密通信机制，支持文本和图像输入，连接到Deepseek、OpenAI、Google Gemini等AI服务。主要特点是所有客户端和服务器之间的通信都经过加密处理，确保数据传输安全。支持Gemini 2.5 Pro的思考模式和多模态处理功能。

## 2. 系统架构

系统主要由以下几个部分组成：

- **配置管理**：负责加载和管理系统配置
- **加密服务**：处理数据加密和解密
- **API层**：提供HTTP接口
- **AI服务**：连接到Deepseek、OpenAI等AI服务
- **Gemini服务**：连接到Google Gemini 2.5 Pro，支持思考模式和多模态处理
- **OCR服务**：使用阿里云和腾讯云OCR API处理图像识别

## 3. 配置说明

配置文件位于 `config/config.yaml`，主要包含以下配置项：

```yaml
server:
  port: 8080              # 服务器监听端口
  timeout: 60s            # 请求超时时间
  debug: true             # 是否开启调试模式

security:
  encryption_key: "..."   # 加密密钥，32字节
  encryption_iv: "..."    # 加密IV值，16字节
  enable_https: false     # 是否启用HTTPS
  cert_file: "..."        # 证书文件路径
  key_file: "..."         # 密钥文件路径

ai:
  api_url: "..."          # AI服务API地址
  api_key: "..."          # AI服务API密钥
  model: "..."            # 使用的模型名称
  system_prompt: "..."    # 系统提示词
  timeout: 30s            # AI请求超时时间

gemini:
  api_key: "..."          # Gemini API密钥
  model: "gemini-2.5-pro" # Gemini模型名称
  system_prompt: "..."    # 系统提示词
  timeout: 60s            # Gemini请求超时时间
  enable_thinking: true   # 是否启用思考模式
  thinking_budget: 1000   # 思考预算

ocr:
  provider: "tencent"                             # OCR提供商：aliyun 或 tencent
  timeout: 30s                                    # OCR处理超时时间

  # 阿里云OCR配置
  aliyun_access_key_id: "..."                    # 阿里云AccessKey ID
  aliyun_access_key_secret: "..."                # 阿里云AccessKey Secret
  aliyun_endpoint: "ocr-api.cn-hangzhou.aliyuncs.com"  # 阿里云OCR API端点
  aliyun_ocr_type: "Advanced"                    # OCR识别类型（Advanced/General等）

  # 腾讯云OCR配置
  tencent_secret_id: "..."                       # 腾讯云SecretId
  tencent_secret_key: "..."                      # 腾讯云SecretKey
  tencent_region: "ap-beijing"                   # 腾讯云地域
  tencent_ocr_type: "GeneralAccurateOCR"         # 腾讯云OCR类型
```

## 4. Gemini配置

### 4.1 Gemini服务配置

本项目支持Google Gemini 2.5 Pro模型，提供思考模式和多模态处理功能。需要在Google AI Studio获取API密钥。

### 4.2 配置说明

Gemini相关配置在 `config/config.yaml` 的 `gemini` 部分：

```yaml
gemini:
  api_key: "your-gemini-api-key-here"  # Gemini API密钥
  model: "gemini-2.5-pro"              # 使用的Gemini模型（可选：gemini-2.5-flash）
  system_prompt: "你是一个有用的AI助手。"  # 系统提示词
  timeout: 60s                         # 请求超时时间
  enable_thinking: true                # 是否启用思考模式
  thinking_budget: 1000                # 思考预算（0表示禁用思考）
```

**模型选择建议**：
- `gemini-2.5-flash`：免费模型，每日250次请求限制，适合开发测试
- `gemini-2.5-pro`：付费模型，需要GCP付款方式，性能更强，适合生产环境

### 4.3 思考模式

- **启用思考**：设置 `enable_thinking: true` 和 `thinking_budget > 0`
- **禁用思考**：设置 `enable_thinking: false` 或 `thinking_budget: 0`
- **思考预算**：控制思考的深度，数值越大思考越深入

### 4.4 多模态功能

Gemini支持直接处理图片，无需OCR预处理：
- 支持JPEG、PNG、GIF、WebP等格式
- 可同时处理图片和文本
- 使用 `gemini-img` 请求类型

### 4.5 重要注意事项

#### 地区限制
- 确保您的API请求地区在可用地区内
- 参考：[Gemini API可用地区](https://ai.google.dev/gemini-api/docs/available-regions?hl=zh-cn#available_regions)

#### 模型选择和定价
- **Gemini 2.5 Flash**：每日免费250次请求
- **Gemini 2.5 Pro**：需要绑定GCP付款方式后才可用
- 详细定价：[Gemini API定价](https://ai.google.dev/gemini-api/docs/pricing?hl=zh-cn)

#### 速率限制
- 不同模型有不同的速率限制
- 建议合理控制请求频率
- 参考：[Gemini API速率限制](https://ai.google.dev/gemini-api/docs/rate-limits?hl=zh-cn)

#### 图片处理能力
- 支持多种图片格式的理解和分析
- 可处理图片中的文字、物体、场景等
- 详细说明：[Gemini图片理解](https://ai.google.dev/gemini-api/docs/image-understanding?hl=zh-cn)

## 5. OCR配置

### 5.1 OCR服务配置

本项目支持阿里云和腾讯云OCR服务，可通过 `provider` 配置项选择使用的服务商。

### 5.2 阿里云OCR配置

需要在阿里云控制台开通OCR服务并获取访问凭证：

```yaml
ocr:
  provider: "aliyun"
  aliyun_access_key_id: "your-access-key-id"
  aliyun_access_key_secret: "your-access-key-secret"
  aliyun_endpoint: "ocr-api.cn-hangzhou.aliyuncs.com"
  aliyun_ocr_type: "Advanced"
```

### 5.3 腾讯云OCR配置

需要在腾讯云控制台开通OCR服务并获取访问凭证：

```yaml
ocr:
  provider: "tencent"
  tencent_secret_id: "your-secret-id"
  tencent_secret_key: "your-secret-key"
  tencent_region: "ap-beijing"
  tencent_ocr_type: "GeneralAccurateOCR"
```

### 5.4 支持的OCR类型

**阿里云**：
- `Advanced`：通用文字识别高精版（默认）
- `General`：通用文字识别基础版

**腾讯云**：
- `GeneralAccurateOCR`：通用印刷体识别（高精度版）
- `GeneralBasicOCR`：通用印刷体识别（基础版）

## 5. 加密机制详解

### 5.1 加密算法

系统使用AES-GCM加密算法，具有以下特点：

- AES-256加密：使用256位密钥
- GCM模式：提供认证加密，确保数据完整性和机密性
- 固定IV值：使用配置文件中指定的IV值，而非随机生成

### 5.2 加密配置

加密相关的配置项在 `config/config.yaml` 的 `security` 部分：

```yaml
security:
  encryption_key: "key"  # 32字节的加密密钥
  encryption_iv: "iv"                  # 固定的IV值（16字节）
```

**重要说明**：
- `encryption_key` 必须是至少32字节长的字符串
- `encryption_iv` 必须是至少12字节长的字符串，系统将使用前12个字节作为nonce

### 5.3 加密过程

1. 创建AES-256密码块
2. 使用GCM模式
3. 使用固定的IV值（从配置文件读取）
4. 加密明文数据
5. 将加密后的数据进行Base64编码

### 5.4 解密过程

1. Base64解码密文
2. 提取nonce部分（前12字节）
3. 使用相同的密钥和nonce进行GCM解密
4. 返回明文

## 6. API接口说明

### 6.1 基本通信流程

1. 客户端使用共享的加密密钥和IV值加密请求内容
2. 服务器接收加密内容，解密后处理请求
3. 服务器加密响应内容，返回给客户端
4. 客户端解密响应内容

### 6.2 请求格式

```json
{
  "type": "text|image|gemini|gemini-img",  // 请求类型
  "content": "..."                         // 加密后的内容
}
```

### 6.3 请求类型说明

| 类型 | 说明 | 处理方式 |
|------|------|----------|
| `text` | 普通文本请求 | 使用配置的AI服务处理 |
| `image` | 图片请求 | OCR识别后发送给AI服务 |
| `gemini` | Gemini文本请求 | 使用Gemini 2.5 Pro处理 |
| `gemini-img` | Gemini图片请求 | Gemini多模态直接处理图片 |

### 6.4 响应格式

```json
{
  "success": true,       // 是否成功
  "message": "...",      // 错误信息（仅当success为false时）
  "data": "..."          // 加密后的响应数据（仅当success为true时）
}
```

### 6.5 客户端下载

服务器提供了一个接口，用于下载客户端的二进制文件：

```http
GET /api/gc/:filename
```


## 7. 安全注意事项

1. **密钥管理**：确保加密密钥和IV值安全存储，可考虑使用环境变量传递
2. **通信安全**：生产环境建议启用HTTPS
3. **日志安全**：调试模式下会记录完整的明文和密文，生产环境应关闭
4. **固定IV**：虽然使用固定IV简化了实现，但在高安全要求场景下需考虑IV的随机性
5. **OCR凭证安全**：阿里云AccessKey和腾讯云SecretKey应通过环境变量配置，避免硬编码
6. **Gemini API密钥安全**：
   - Gemini API密钥应通过环境变量 `GEMINI_API_KEY` 配置
   - 定期轮换API密钥
   - 监控API使用量，防止异常调用
   - 注意地区限制，确保合规使用

## 8. 错误处理

常见错误及解决方案：

| 错误信息 | 可能原因 | 解决方案 |
|---------|---------|---------|
| 加密密钥长度不足 | 配置的密钥长度小于32字节 | 使用至少32字节长的密钥 |
| IV值长度不足 | 配置的IV值长度小于12字节 | 使用至少12字节长的IV值 |
| 密文长度不足 | 密文被截断或损坏 | 检查网络传输是否完整 |
| 解密失败 | 密钥或IV不匹配 | 确保客户端和服务器使用相同的密钥和IV |
| Aliyun OCR client is not initialized | 阿里云凭证配置错误 | 检查AccessKey ID和Secret配置 |
| OCR API调用失败 | 网络问题或API配额不足 | 检查网络连接和云服务账户余额 |
| Gemini服务未可用 | Gemini API密钥未配置或无效 | 检查GEMINI_API_KEY环境变量或配置文件 |
| Gemini API调用失败 | 地区限制或配额不足 | 检查地区可用性和API配额限制 |
| Gemini请求超时 | 网络延迟或思考预算过大 | 增加timeout或减少thinking_budget |
| 403 Forbidden | API密钥无效或地区不支持 | 验证API密钥和请求地区 |
| 429 Too Many Requests | 超出速率限制 | 降低请求频率或升级配额 |

## 9. 测试

建议进行以下测试：

### 9.1 基础功能测试
1. 使用相同密钥和IV进行加解密测试
2. 验证长文本和二进制数据的加解密
3. 测试不同密钥和IV组合的错误情况
4. 性能测试（大量并发请求下的加解密性能）

### 9.2 OCR功能测试
5. OCR API测试（不同图片格式和内容）
6. OCR识别准确性测试
7. 阿里云和腾讯云OCR服务切换测试

### 9.3 Gemini功能测试
8. **Gemini文本处理测试**：
   ```bash
   go run client.go -mode gemini -input "你好，请介绍一下自己"
   ```

9. **Gemini多模态测试**：
   ```bash
   go run client.go -mode gemini-img -input "test_image.jpg"
   ```

10. **思考模式测试**：
    - 测试启用和禁用思考模式的差异
    - 测试不同thinking_budget值的效果

11. **Gemini错误处理测试**：
    - 测试无效API密钥的错误处理
    - 测试超出配额限制的错误处理
    - 测试不支持地区的错误处理

12. **性能对比测试**：
    - 对比传统OCR+AI和Gemini多模态的处理速度
    - 测试不同图片大小和复杂度的处理时间

## 10. 扩展与定制

如需修改加密算法、OCR服务或Gemini服务，可以在以下文件中进行更改：

### 10.1 核心服务文件
- `utils/encryption.go`：加密核心实现
- `service/encryption.go`：加密服务封装
- `service/ocr_service.go`：阿里云OCR服务实现
- `service/tencent_ocr_service.go`：腾讯云OCR服务实现
- `service/gemini_service.go`：Gemini服务实现
- `service/ai_service.go`：传统AI服务实现

### 10.2 配置和模型文件
- `config/config.go`：配置结构定义
- `model/request.go`：请求模型定义
- `model/response.go`：响应模型定义

### 10.3 API处理文件
- `api/handler.go`：API请求处理逻辑
- `api/router.go`：路由配置

### 10.4 Gemini定制建议
- **模型切换**：在配置中修改model字段，支持gemini-2.5-flash和gemini-2.5-pro
- **思考模式调优**：根据使用场景调整thinking_budget值
- **多模态优化**：可在gemini_service.go中添加更多图片格式支持
- **错误处理增强**：可添加更详细的Gemini API错误码处理

## 11. 官方文档参考

- [Gemini API可用地区](https://ai.google.dev/gemini-api/docs/available-regions?hl=zh-cn#available_regions)
- [Gemini API定价](https://ai.google.dev/gemini-api/docs/pricing?hl=zh-cn)
- [Gemini图片理解](https://ai.google.dev/gemini-api/docs/image-understanding?hl=zh-cn)
- [Gemini API速率限制](https://ai.google.dev/gemini-api/docs/rate-limits?hl=zh-cn)
- [Google AI Studio](https://aistudio.google.com/)
- [阿里云OCR文档](https://ocr.console.aliyun.com/overview)
- [腾讯云OCR文档](https://cloud.tencent.com/product/ocr)


