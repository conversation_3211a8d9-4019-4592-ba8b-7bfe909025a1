# UIAccess AI 客户端功能说明

## 🎯 功能概述

UIAccess 应用现在集成了完整的 AI 客户端功能，支持：

1. **剪切板内容发送** - 通过右键菜单发送剪切板文本到后端AI
2. **加密通信** - 使用 AES-256-GCM 加密算法保护数据传输
3. **实时状态显示** - 窗口实时显示AI处理状态和响应内容
4. **隐蔽截图功能** - 预留接口，支持图片发送（待实现）

## 🏗️ 架构设计

### 模块结构
```
src/
├── config.rs          # 配置管理（服务器URL、密钥等）
├── encryption.rs       # AES-GCM加密/解密
├── http_client.rs      # HTTP通信客户端
├── ai_client.rs        # AI客户端高级接口
└── simple_test.rs      # UI集成和用户交互
```

### 数据流程
```
用户操作 → 剪切板读取 → AES加密 → HTTP发送 → 后端处理 → 加密响应 → 解密显示
```

## 🔧 配置说明

### 环境变量配置
在 `src/config.rs` 中可以修改以下配置：

```rust
// 开发环境配置
pub fn development_config() -> AppConfig {
    AppConfig::new()
        .with_server_url("http://localhost:8080")           // 后端服务器地址
        .with_encryption_key("development-key-32-bytes-long-12345")  // 32字节加密密钥
        .with_encryption_iv("dev-iv-12byte")                // 12字节IV
        .with_timeout(30)                                   // 30秒超时
        .with_debug(true)                                   // 开启调试模式
}
```

### 生产环境配置
```rust
// 修改为你的生产环境配置
.with_server_url("https://your-production-server.com")
.with_encryption_key("your-production-32-byte-key-here")
.with_encryption_iv("your-prod-iv")
.with_timeout(60)
.with_debug(false)
```

## 🔐 加密机制

### AES-256-GCM 加密
- **算法**: AES-256-GCM（与后端完全兼容）
- **密钥长度**: 32字节
- **IV/Nonce长度**: 12字节
- **编码**: Base64传输

### 加密流程
1. 明文 → AES-GCM加密 → 密文
2. Nonce(12字节) + 密文 → Base64编码
3. 发送到后端API

### 解密流程
1. Base64解码 → 原始数据
2. 提取Nonce(前12字节) + 密文
3. AES-GCM解密 → 明文

## 📡 API接口

### 请求格式
```json
{
    "type": "text",
    "content": "base64_encrypted_content"
}
```

### 响应格式
```json
{
    "success": true,
    "data": "base64_encrypted_response"
}
```

## 🎮 使用方法

### 1. 启动应用
```bash
cargo run --bin uiaccess_app
```

### 2. 发送剪切板内容
1. 复制任意文本到剪切板
2. 右键点击窗口
3. 选择"发送剪切板内容"
4. 观察窗口状态变化：
   - "读取剪切板..." 
   - "发送中..."
   - "等待响应..."
   - "响应已接收"

### 3. 查看AI响应
- AI响应会实时显示在窗口中
- 最多显示10行内容
- 超长行会自动截断

## 🔍 状态指示

### AI状态显示
- **已连接** - AI客户端初始化成功
- **连接失败** - 无法连接到后端服务器
- **读取剪切板...** - 正在读取剪切板内容
- **发送中...** - 正在加密和发送数据
- **等待响应...** - 等待服务器响应
- **响应已接收** - 成功接收并解密响应

## 🐛 故障排除

### 常见问题

#### 1. "连接失败"
- 检查后端服务器是否运行在 `http://localhost:8080`
- 确认网络连接正常
- 检查防火墙设置

#### 2. "加密密钥长度不足"
- 确保密钥至少32字节
- 确保IV至少12字节
- 检查 `config.rs` 中的配置

#### 3. "剪切板为空"
- 确保剪切板中有文本内容
- 尝试重新复制文本

#### 4. "发送失败"
- 检查服务器状态
- 查看调试输出获取详细错误信息
- 确认API端点正确

### 调试模式
开启调试模式可以看到详细的执行日志：
```rust
.with_debug(true)
```

调试输出包括：
- 加密/解密过程
- HTTP请求/响应
- 详细错误信息

## 🚀 扩展功能

### 图片发送（预留）
```rust
// 已实现但未集成到UI
client.send_image(base64_image_data).await
```

### 自定义加密
```rust
// 可以轻松更换加密算法
impl Encryptor {
    pub fn encrypt(&self, plaintext: &str) -> Result<String, String>
    pub fn decrypt(&self, ciphertext: &str) -> Result<String, String>
}
```

### 多服务器支持
```rust
// 可以配置多个后端服务器
let config1 = AppConfig::new().with_server_url("http://server1:8080");
let config2 = AppConfig::new().with_server_url("http://server2:8080");
```

## 📝 开发说明

### 添加新功能
1. 在 `ai_client.rs` 中添加新方法
2. 在 `simple_test.rs` 中添加UI集成
3. 在菜单中添加新选项

### 修改加密算法
1. 更新 `encryption.rs` 中的实现
2. 确保与后端保持兼容
3. 更新密钥和IV长度要求

### 自定义配置
1. 修改 `config.rs` 中的默认值
2. 添加环境变量支持
3. 实现配置文件加载

## ✅ 测试验证

### 功能测试清单
- [ ] AI客户端初始化
- [ ] 服务器连接测试
- [ ] 剪切板内容读取
- [ ] 文本加密/解密
- [ ] HTTP请求发送
- [ ] 响应接收和显示
- [ ] 错误处理和状态更新

### 性能测试
- 加密/解密速度
- 网络请求延迟
- 内存使用情况
- UI响应性能

这个AI客户端功能为UIAccess应用提供了强大的AI交互能力，支持安全的加密通信和用户友好的界面操作。
