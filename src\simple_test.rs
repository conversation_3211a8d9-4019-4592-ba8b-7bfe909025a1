use windows::Win32::{
    Foundation::{CO<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, WPARA<PERSON>, LRESULT, POINT, TRUE},
    Graphics::Gdi::ScreenToClient,
    UI::Input::KeyboardAndMouse::{GetAsyncKeyState, VK_RBUTTON},
    UI::WindowsAndMessaging::{
        <PERSON>reateWindowExW, DefWindowProcW, RegisterClassW, ShowWindow,
        GetMessageW, TranslateMessage, DispatchMessageW, PostQuitMessage,
        SetWindowPos, SetLayeredWindowAttributes, GetWindowRect, GetClientRect, CreatePopupMenu,
        AppendMenuW, TrackPopupMenu, DestroyMenu, GetCursorPos, SetForegroundWindow,
        LoadCursorW, MSG, WNDCLASSW, WS_POPUP, WS_VISIBLE, WS_EX_LAYERED,
        WS_EX_TOOLWINDOW, WS_EX_TOPMOST, HWND_TOPMOST, HW<PERSON>_TOP, SW<PERSON>_NOMOVE, SWP_NOSIZE,
        SW<PERSON>_NOACTIVATE, LWA_ALPHA, SW_SHOW, WM_DESTROY, WM_PAINT, WM_COMMAND,
        WM_LBUTTONDOWN, WM_LBUTTONUP, WM_MOUSEMOVE, WM_RBUTTONDOWN, WM_RBUTTONUP,
        WM_NCLBUTTONDOWN, WM_NCLBUTTONUP, WM_MOUSEWHEEL,
        WM_NCHITTEST, WM_GETMINMAXINFO, WM_KEYDOWN, WM_HOTKEY,
        SW_HIDE,
        HTCAPTION, HTLEFT, HTRIGHT, HTTOP, HTBOTTOM,
        HTTOPLEFT, HTTOPRIGHT, HTBOTTOMLEFT, HTBOTTOMRIGHT, MINMAXINFO,
        IDC_ARROW, CS_HREDRAW, CS_VREDRAW,
        MF_STRING, MF_SEPARATOR, MF_POPUP, TPM_RIGHTBUTTON, TPM_RETURNCMD, HTCLIENT,
    },
    Graphics::Gdi::{
        BeginPaint, EndPaint, TextOutW, SetTextColor, SetBkMode, CreateSolidBrush,
        DeleteObject, InvalidateRect, FillRect, UpdateWindow, PAINTSTRUCT, TRANSPARENT,
    },
    System::LibraryLoader::{GetModuleHandleW, GetModuleFileNameW},
    System::Threading::{CreateProcessW, PROCESS_INFORMATION, STARTUPINFOW},
    System::Diagnostics::ToolHelp::{
        CreateToolhelp32Snapshot, Process32FirstW, Process32NextW,
        PROCESSENTRY32W, TH32CS_SNAPPROCESS,
    },
    Foundation::CloseHandle,
    System::Threading::{OpenProcess, TerminateProcess, PROCESS_TERMINATE},
    UI::Input::KeyboardAndMouse::{RegisterHotKey, UnregisterHotKey, HOT_KEY_MODIFIERS, VK_CONTROL, VK_SHIFT, VK_X},
    System::Registry::{RegOpenKeyExW, RegDeleteValueW, RegCloseKey, HKEY_CURRENT_USER, KEY_SET_VALUE},
    UI::WindowsAndMessaging::{EnumWindows, GetWindowThreadProcessId, GetWindowLongW, GWL_EXSTYLE, HWND_NOTOPMOST},
    System::Threading::{PROCESS_QUERY_INFORMATION, PROCESS_VM_READ},
    System::ProcessStatus::GetModuleFileNameExW,

};

// AI客户端相关导入
use crate::config::development_config;
use crate::ai_client::AiClient;
use crate::screenshot::{ScreenCapture, ScreenshotArea};
use crate::{PrivilegeLevel, PrivilegeResult, prepare_optimal_privileges, get_windows_version};
use std::sync::Arc;

static mut WINDOW_HWND: Option<HWND> = None;
static mut CURRENT_ALPHA: u8 = 64; // 当前透明度
static mut SHOW_DEBUG_INFO: bool = true; // 是否显示调试信息
static mut IS_WINDOW_VISIBLE: bool = true; // 窗口可见状态跟踪
static mut IS_DARK_THEME: bool = false; // 主题状态：true=黑底白字，false=白底黑字

// AI客户端相关全局变量
static mut AI_CLIENT: Option<Arc<AiClient>> = None;
static mut AI_STATUS: String = String::new();
static mut AI_RESPONSE: String = String::new();

// 双击检测相关变量
static mut LAST_CLICK_TIME: u64 = 0; // 上次点击时间 (毫秒)
static mut LAST_CLICK_X: i32 = 0; // 上次点击X坐标
static mut LAST_CLICK_Y: i32 = 0; // 上次点击Y坐标
static mut CLICK_COUNT: u32 = 0; // 连续点击计数

// 自毁模式状态
static mut SELF_DESTRUCT_FIRST_HOTKEY_TIME: u64 = 0; // 第一次按下 Ctrl+Shift+X 的时间
static mut SELF_DESTRUCT_MENU_CLICKED_ONCE: bool = false; // 菜单是否已点击一次
static mut SELF_DESTRUCT_MENU_FIRST_CLICK_TIME: u64 = 0; // 菜单第一次点击时间

// 退出确认状态
static mut EXIT_CONFIRMATION_CLICKED_ONCE: bool = false;

// 滚动相关变量
static mut SCROLL_OFFSET: i32 = 0; // 滚动偏移量（像素）
static mut TOTAL_TEXT_HEIGHT: i32 = 0; // 文本总高度

// 截图相关变量
static mut SCREENSHOT_DATA: String = String::new(); // 截图的base64数据

/// 从截图模块接收截图数据的C接口函数
#[no_mangle]
pub extern "C" fn save_screenshot_data(data: *const u8, len: usize) {
    unsafe {
        if !data.is_null() && len > 0 {
            let slice = std::slice::from_raw_parts(data, len);
            if let Ok(data_str) = std::str::from_utf8(slice) {
                SCREENSHOT_DATA = data_str.to_string();
                println!("💾 截图数据已保存到全局变量，长度: {}", SCREENSHOT_DATA.len());
            } else {
                println!("❌ 截图数据转换失败：无效的UTF-8");
            }
        } else {
            println!("❌ 截图数据保存失败：空指针或长度为0");
        }
    }
}

/// 内部函数：直接保存截图数据
pub unsafe fn save_screenshot_data_internal(data: String) {
    SCREENSHOT_DATA = data;
    println!("💾 截图数据已保存到全局变量，长度: {}", SCREENSHOT_DATA.len());
}

// 边缘检测区域大小（像素）
const EDGE_SIZE: i32 = 8;

// 菜单命令常量
const MENU_ALPHA_25: u32 = 1001;
const MENU_ALPHA_50: u32 = 1002;
const MENU_ALPHA_75: u32 = 1003;
const MENU_ALPHA_100: u32 = 1004;
const MENU_RESET_POSITION: u32 = 1005;
const MENU_RESET_SIZE: u32 = 1006;
const MENU_TOGGLE_DEBUG: u32 = 1007;
const MENU_TOGGLE_THEME: u32 = 1008;
const MENU_RESTART_EXPLORER: u32 = 1009;
const MENU_REMOVE_CXEXAM_TOPMOST: u32 = 1010;
const MENU_TOGGLE_VISIBILITY: u32 = 1011;
const MENU_EXIT: u32 = 1012;
const MENU_SELF_DESTRUCT: u32 = 1013;
const MENU_SEND_CLIPBOARD: u32 = 1014; // 发送剪切板内容
const MENU_SEND_CLIPBOARD_DETAILED: u32 = 1018; // 发送剪切板内容（详细）
const MENU_CLEAR_TEXT: u32 = 1015; // 清空文本
const MENU_SCREENSHOT: u32 = 1016; // 区域截图
const MENU_WIN_SCREENSHOT: u32 = 1017; // Windows自带截图
const MENU_COPY_TEXT: u32 = 1019; // 复制窗口文字

// 热键常量
const HOTKEY_TOGGLE_VISIBILITY: i32 = 1; // 热键ID
const HOTKEY_SEND_CLIPBOARD: i32 = 2; // ALT+S 发送剪切板
const HOTKEY_CLEAR_TEXT: i32 = 3; // ALT+C 清空文本
const HOTKEY_SCREENSHOT: i32 = 4; // ALT+A 区域截图
const HOTKEY_TOGGLE_VISIBILITY_BACKUP: i32 = 5; // CTRL+/ 备用显示/隐藏
const HOTKEY_DECREASE_ALPHA: i32 = 6; // CTRL+- 降低透明度
const HOTKEY_INCREASE_ALPHA: i32 = 7; // CTRL++ 增加透明度
const VK_1: u32 = 0x31; // 数字键 1
const VK_S: u32 = 0x53; // S键
const VK_C: u32 = 0x43; // C键
const VK_A: u32 = 0x41; // A键
const VK_SLASH: u32 = 0xBF; // 斜杠键 /
const VK_MINUS: u32 = 0xBD; // 减号键 -
const VK_PLUS: u32 = 0xBB; // 加号键 + (等号键)

// 双击检测常量
const DOUBLE_CLICK_TIME: u64 = 500; // 双击时间间隔 (毫秒)
const DOUBLE_CLICK_DISTANCE: i32 = 5; // 双击位置容差 (像素)

// 自毁模式常量
const SELF_DESTRUCT_HOTKEY_INTERVAL: u64 = 2000; // Ctrl+Shift+X 双击间隔 (毫秒)
const SELF_DESTRUCT_COUNTDOWN: u32 = 1; // 自毁倒计时 (秒)

// 窗口枚举数据结构
#[derive(Default)]
struct WindowEnumData {
    target_windows: Vec<HWND>,
    found_count: u32,
    processed_count: u32,
}

// 最小窗口尺寸
const MIN_WINDOW_WIDTH: i32 = 100;
const MIN_WINDOW_HEIGHT: i32 = 100;

// 注意：IS_RIGHT_BUTTON_DOWN 变量已移除，未使用

/// 基于 WM_NCHITTEST 的原生边缘检测
unsafe fn hit_test_nca(hwnd: HWND, x: i32, y: i32) -> LRESULT {
    // 转换为窗口相对坐标
    let mut point = POINT { x, y };
    if !ScreenToClient(hwnd, &mut point).as_bool() {
        return LRESULT(HTCLIENT as isize);
    }

    // 获取窗口客户区大小
    let mut client_rect = windows::Win32::Foundation::RECT::default();
    if !GetClientRect(hwnd, &mut client_rect).as_bool() {
        return LRESULT(HTCLIENT as isize);
    }

    let width = client_rect.right;
    let height = client_rect.bottom;

    // 动态调整边缘大小
    let edge_size = if width <= 120 || height <= 120 {
        4  // 小窗口使用较小的边缘
    } else {
        EDGE_SIZE
    };

    // 检测边缘位置
    let left = point.x <= edge_size;
    let right = point.x >= width - edge_size;
    let top = point.y <= edge_size;
    let bottom = point.y >= height - edge_size;

    // 检查是否在边缘区域
    let is_on_edge = left || right || top || bottom;

    // 如果正在处理右键，或者在边缘区域，返回相应的结果
    if is_on_edge {
        // 返回对应的边缘命中测试结果
        if top && left {
            LRESULT(HTTOPLEFT as isize)
        } else if top && right {
            LRESULT(HTTOPRIGHT as isize)
        } else if bottom && left {
            LRESULT(HTBOTTOMLEFT as isize)
        } else if bottom && right {
            LRESULT(HTBOTTOMRIGHT as isize)
        } else if left {
            LRESULT(HTLEFT as isize)
        } else if right {
            LRESULT(HTRIGHT as isize)
        } else if top {
            LRESULT(HTTOP as isize)
        } else if bottom {
            LRESULT(HTBOTTOM as isize)
        } else {
            LRESULT(HTCLIENT as isize)
        }
    } else {
        // 中央区域：检查鼠标按钮状态
        let right_button_state = GetAsyncKeyState(VK_RBUTTON.0 as i32);

        // 如果右键被按下，返回 HTCLIENT 以允许右键消息通过
        if (right_button_state & 0x8000u16 as i16) != 0 {
            LRESULT(HTCLIENT as isize)
        } else {
            LRESULT(HTCAPTION as isize)  // 中央区域用于拖拽
        }
    }
}

/// 设置最小窗口尺寸限制
unsafe fn set_min_max_info(lparam: LPARAM) {
    let minmax = lparam.0 as *mut MINMAXINFO;
    if !minmax.is_null() {
        (*minmax).ptMinTrackSize.x = MIN_WINDOW_WIDTH;
        (*minmax).ptMinTrackSize.y = MIN_WINDOW_HEIGHT;
    }
}

/// 自定义双击检测函数
unsafe fn detect_double_click(x: i32, y: i32) -> bool {
    let current_time = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap_or_default()
        .as_millis() as u64;

    // 计算与上次点击的时间差
    let time_diff = current_time - LAST_CLICK_TIME;

    // 计算与上次点击的距离差
    let distance_x = (x - LAST_CLICK_X).abs();
    let distance_y = (y - LAST_CLICK_Y).abs();
    let distance = ((distance_x * distance_x + distance_y * distance_y) as f64).sqrt() as i32;

    if SHOW_DEBUG_INFO {
        println!("🖱️ 点击检测: 时间差={}ms, 距离={}px, 计数={}", time_diff, distance, CLICK_COUNT);
    }

    // 检查是否在双击时间和距离范围内
    if time_diff <= DOUBLE_CLICK_TIME && distance <= DOUBLE_CLICK_DISTANCE {
        CLICK_COUNT += 1;
        if CLICK_COUNT >= 2 {
            // 检测到双击，重置计数
            CLICK_COUNT = 0;
            LAST_CLICK_TIME = 0;
            println!("🖱️ ✅ 检测到双击！");
            return true;
        }
    } else {
        // 超出时间或距离范围，重置计数
        CLICK_COUNT = 1;
    }

    // 更新上次点击信息
    LAST_CLICK_TIME = current_time;
    LAST_CLICK_X = x;
    LAST_CLICK_Y = y;

    false
}

/// 窗口过程函数
unsafe extern "system" fn window_proc(
    hwnd: HWND,
    msg: u32,
    wparam: WPARAM,
    lparam: LPARAM,
) -> LRESULT {
    // 调试：记录所有鼠标消息
    if SHOW_DEBUG_INFO && (msg == WM_RBUTTONDOWN || msg == WM_RBUTTONUP || msg == WM_LBUTTONDOWN || msg == WM_LBUTTONUP || msg == WM_NCLBUTTONDOWN || msg == WM_NCLBUTTONUP) {
        let msg_name = match msg {
            WM_LBUTTONDOWN => "WM_LBUTTONDOWN",
            WM_LBUTTONUP => "WM_LBUTTONUP",
            WM_NCLBUTTONDOWN => "WM_NCLBUTTONDOWN",
            WM_NCLBUTTONUP => "WM_NCLBUTTONUP",
            WM_RBUTTONDOWN => "WM_RBUTTONDOWN",
            WM_RBUTTONUP => "WM_RBUTTONUP",
            _ => "UNKNOWN",
        };
        println!("🖱️ 收到鼠标消息: {} ({}), wparam={}, lparam={}", msg_name, msg, wparam.0, lparam.0);
    }

    match msg {
        WM_DESTROY => {
            // 注销所有热键
            UnregisterHotKey(hwnd, HOTKEY_TOGGLE_VISIBILITY);
            UnregisterHotKey(hwnd, HOTKEY_SEND_CLIPBOARD);
            UnregisterHotKey(hwnd, HOTKEY_CLEAR_TEXT);
            UnregisterHotKey(hwnd, HOTKEY_SCREENSHOT);
            UnregisterHotKey(hwnd, HOTKEY_TOGGLE_VISIBILITY_BACKUP);
            UnregisterHotKey(hwnd, HOTKEY_DECREASE_ALPHA);
            UnregisterHotKey(hwnd, HOTKEY_INCREASE_ALPHA);
            println!("🔧 所有热键已注销");
            PostQuitMessage(0);
            LRESULT(0)
        }
        WM_NCHITTEST => {
            // 使用原生的边缘检测
            let x = (lparam.0 & 0xFFFF) as i16 as i32;
            let y = ((lparam.0 >> 16) & 0xFFFF) as i16 as i32;
            let result = hit_test_nca(hwnd, x, y);

            result
        }
        WM_GETMINMAXINFO => {
            // 设置最小窗口尺寸
            set_min_max_info(lparam);
            LRESULT(0)
        }
        WM_PAINT => {
            let mut ps = PAINTSTRUCT::default();
            let hdc = BeginPaint(hwnd, &mut ps);

            if !hdc.is_invalid() {
                // 设置背景为透明
                SetBkMode(hdc, TRANSPARENT);

                // 根据主题选择背景色和文字色
                let (bg_color, text_color, _theme_name) = if IS_DARK_THEME {
                    (0x80000000u32, 0x00FFFFFFu32, "黑底白字") // 半透明黑色背景，白色文字
                } else {
                    (0x80FFFFFFu32, 0x00000000u32, "白底黑字") // 半透明白色背景，黑色文字
                };

                // 创建主题背景 - 使用 FillRect 避免边框
                let brush = CreateSolidBrush(COLORREF(bg_color));

                // 获取窗口客户区大小
                let mut client_rect = windows::Win32::Foundation::RECT::default();
                if GetClientRect(hwnd, &mut client_rect).as_bool() {
                    // 使用 FillRect 填充背景，不绘制边框
                    FillRect(hdc, &client_rect, brush);
                } else {
                    // 如果获取失败，使用默认大小
                    let default_rect = windows::Win32::Foundation::RECT {
                        left: 0,
                        top: 0,
                        right: 100,
                        bottom: 100,
                    };
                    FillRect(hdc, &default_rect, brush);
                }

                DeleteObject(brush);

                // 设置文字颜色
                SetTextColor(hdc, COLORREF(text_color));

                // 显示AI响应内容或连接状态，正确处理换行符，支持滚动
                let display_text = if !AI_RESPONSE.is_empty() {
                    AI_RESPONSE.clone()
                } else {
                    // 如果没有AI响应，显示连接状态
                    AI_STATUS.clone()
                };

                if !display_text.is_empty() {
                    let line_height = 20; // 行高
                    let chars_per_line = 15; // 每行字符数
                    let start_y = 20; // 起始Y位置

                    // 获取窗口客户区高度
                    let mut client_rect = windows::Win32::Foundation::RECT::default();
                    let window_height = if GetClientRect(hwnd, &mut client_rect).as_bool() {
                        client_rect.bottom - client_rect.top
                    } else {
                        100 // 默认高度
                    };

                    // 首先按换行符分割文本
                    let paragraphs: Vec<&str> = display_text.split('\n').collect();
                    let mut display_lines = Vec::new();

                    // 处理每个段落
                    for paragraph in paragraphs {
                        if paragraph.is_empty() {
                            // 空行
                            display_lines.push(String::new());
                        } else {
                            // 将段落按字符数分割成多行
                            let chars: Vec<char> = paragraph.chars().collect();
                            let mut start = 0;

                            while start < chars.len() {
                                let end = (start + chars_per_line).min(chars.len());
                                let line: String = chars[start..end].iter().collect();
                                display_lines.push(line);
                                start = end;
                            }
                        }
                    }

                    let total_lines = display_lines.len();
                    TOTAL_TEXT_HEIGHT = total_lines as i32 * line_height;

                    // 限制滚动范围
                    let max_scroll = (TOTAL_TEXT_HEIGHT - window_height + start_y).max(0);
                    if SCROLL_OFFSET > max_scroll {
                        SCROLL_OFFSET = max_scroll;
                    }
                    if SCROLL_OFFSET < 0 {
                        SCROLL_OFFSET = 0;
                    }

                    // 绘制所有行
                    for (line_index, line) in display_lines.iter().enumerate() {
                        let y_pos = start_y + (line_index as i32 * line_height) - SCROLL_OFFSET;

                        // 只绘制在可见区域内的文本
                        if y_pos >= -line_height && y_pos < window_height {
                            let line_wide: Vec<u16> = line.encode_utf16().collect();
                            TextOutW(hdc, 20, y_pos, &line_wide);
                        }
                    }
                }
                
                EndPaint(hwnd, &ps);
            }
            
            LRESULT(0)
        }
        WM_NCLBUTTONDOWN => {
            // 处理非客户区左键按下（当 WM_NCHITTEST 返回 HTCAPTION 时会收到此消息）
            let hit_test_result = wparam.0 as u32;

            if hit_test_result == HTCAPTION {
                // 获取鼠标屏幕坐标
                let screen_x = (lparam.0 & 0xFFFF) as i16 as i32;
                let screen_y = ((lparam.0 >> 16) & 0xFFFF) as i16 as i32;

                // 转换为客户区坐标进行双击检测
                let mut point = POINT { x: screen_x, y: screen_y };
                if ScreenToClient(hwnd, &mut point).as_bool() {
                    // 检测双击
                    if detect_double_click(point.x, point.y) {
                        println!("🖱️ 在标题栏区域检测到双击，隐藏窗口");
                        toggle_window_visibility(hwnd);
                        return LRESULT(0);
                    }
                }

                // 单击：让 Windows 处理拖拽（调用默认处理）
                println!("🔧 单击标题栏，让 Windows 处理拖拽");
                return DefWindowProcW(hwnd, msg, wparam, lparam);
            }

            // 其他非客户区点击：让 Windows 处理
            DefWindowProcW(hwnd, msg, wparam, lparam)
        }
        WM_LBUTTONDOWN => {
            // 处理客户区左键按下
            let click_x = (lparam.0 & 0xFFFF) as i16 as i32;
            let click_y = ((lparam.0 >> 16) & 0xFFFF) as i16 as i32;

            println!("�️ 客户区左键点击: ({}, {})", click_x, click_y);

            // 在客户区：检测双击
            if detect_double_click(click_x, click_y) {
                println!("🖱️ 在客户区检测到双击，隐藏窗口");
                toggle_window_visibility(hwnd);
                return LRESULT(0);
            }

            LRESULT(0)
        }
        WM_LBUTTONUP => {
            // 客户区左键释放
            println!("🖱️ 客户区左键释放");
            LRESULT(0)
        }
        WM_NCLBUTTONUP => {
            // 非客户区左键释放
            println!("🖱️ 非客户区左键释放");
            LRESULT(0)
        }
        WM_MOUSEMOVE => {
            // 鼠标移动 - 现在完全由 Windows 处理拖拽和调整大小
            LRESULT(0)
        }
        WM_RBUTTONDOWN => {
            // 显示右键菜单
            println!("🖱️ WM_RBUTTONDOWN 收到！位置: ({}, {})",
                (lparam.0 & 0xFFFF) as i16 as i32,
                ((lparam.0 >> 16) & 0xFFFF) as i16 as i32);
            show_context_menu(hwnd);
            LRESULT(0)
        }
        WM_RBUTTONUP => {
            // 备用右键菜单处理（某些系统可能需要）
            println!("🖱️ WM_RBUTTONUP 收到！位置: ({}, {})",
                (lparam.0 & 0xFFFF) as i16 as i32,
                ((lparam.0 >> 16) & 0xFFFF) as i16 as i32);
            // 也尝试在这里显示菜单，以防 RBUTTONDOWN 被拦截
            show_context_menu(hwnd);
            LRESULT(0)
        }
        WM_COMMAND => {
            // 处理菜单命令
            println!("🔧 收到 WM_COMMAND: {}", wparam.0);
            handle_menu_command(hwnd, wparam.0 as u32);
            LRESULT(0)
        }
        // 注意：WM_LBUTTONDBLCLK 已移除，使用自定义双击检测
        WM_HOTKEY => {
            // 处理全局热键
            let hotkey_id = wparam.0 as i32;
            match hotkey_id {
                HOTKEY_TOGGLE_VISIBILITY => {
                    println!("⌨️ 收到 ALT+1 热键，切换窗口可见性");
                    toggle_window_visibility(hwnd);
                }
                HOTKEY_SEND_CLIPBOARD => {
                    println!("⌨️ 收到 ALT+S 热键，发送剪切板内容");
                    handle_send_clipboard(hwnd);
                }
                HOTKEY_CLEAR_TEXT => {
                    println!("⌨️ 收到 ALT+C 热键，清空文本");
                    handle_clear_text(hwnd);
                }
                HOTKEY_SCREENSHOT => {
                    println!("⌨️ 收到 ALT+A 热键，开始区域截图");
                    handle_screenshot(hwnd);
                }
                HOTKEY_TOGGLE_VISIBILITY_BACKUP => {
                    println!("⌨️ 收到 CTRL+/ 备用热键，切换窗口可见性");
                    toggle_window_visibility(hwnd);
                }
                HOTKEY_DECREASE_ALPHA => {
                    println!("⌨️ 收到 CTRL+- 热键，降低透明度");
                    adjust_alpha(hwnd, -30);
                }
                HOTKEY_INCREASE_ALPHA => {
                    println!("⌨️ 收到 CTRL++ 热键，增加透明度");
                    adjust_alpha(hwnd, 30);
                }
                _ => {}
            }
            LRESULT(0)
        }
        WM_KEYDOWN => {
            // 添加键盘快捷键测试
            let key_code = wparam.0 as u32;
            println!("⌨️ 按键按下: {}", key_code);

            // 按 M 键显示菜单（测试用）
            if key_code == 77 { // 'M' 键
                println!("🔧 通过键盘快捷键显示菜单");
                show_context_menu(hwnd);
            }
            LRESULT(0)
        }
        WM_MOUSEWHEEL => {
            // 处理鼠标滚轮滚动
            let wheel_delta = ((wparam.0 >> 16) & 0xFFFF) as i16 as i32;
            let scroll_lines = 3; // 每次滚动的行数
            let line_height = 20; // 行高

            println!("🖱️ 鼠标滚轮: delta={}", wheel_delta);

            // 向上滚动（wheel_delta > 0）减少滚动偏移
            // 向下滚动（wheel_delta < 0）增加滚动偏移
            if wheel_delta > 0 {
                // 向上滚动
                SCROLL_OFFSET -= scroll_lines * line_height;
                if SCROLL_OFFSET < 0 {
                    SCROLL_OFFSET = 0;
                }
                println!("📜 向上滚动，新偏移: {}", SCROLL_OFFSET);
            } else if wheel_delta < 0 {
                // 向下滚动
                SCROLL_OFFSET += scroll_lines * line_height;
                println!("📜 向下滚动，新偏移: {}", SCROLL_OFFSET);
            }

            // 重绘窗口以显示滚动效果
            InvalidateRect(hwnd, None, TRUE);
            UpdateWindow(hwnd);

            LRESULT(0)
        }

        _ => DefWindowProcW(hwnd, msg, wparam, lparam),
    }
}

/// 显示右键上下文菜单
unsafe fn show_context_menu(hwnd: HWND) {
    println!("🔧 show_context_menu 被调用！");

    let hmenu = match CreatePopupMenu() {
        Ok(menu) => {
            println!("✅ 菜单创建成功，句柄: {:?}", menu);
            menu
        },
        Err(e) => {
            println!("❌ 菜单创建失败: {:?}", e);
            return;
        },
    };

    // 首先添加AI功能选项（最重要的功能）
    AppendMenuW(hmenu, MF_STRING, MENU_SEND_CLIPBOARD as usize, windows::core::w!("发送"));

    // 添加详细发送选项
    AppendMenuW(hmenu, MF_STRING, MENU_SEND_CLIPBOARD_DETAILED as usize, windows::core::w!("发送+"));

    // 第二个添加清空文本选项
    AppendMenuW(hmenu, MF_STRING, MENU_CLEAR_TEXT as usize, windows::core::w!("清空"));

    // 第三个添加复制选项
    AppendMenuW(hmenu, MF_STRING, MENU_COPY_TEXT as usize, windows::core::w!("复制"));

    // 第四个添加截图选项
    AppendMenuW(hmenu, MF_STRING, MENU_SCREENSHOT as usize, windows::core::w!("截图"));

    // 第五个添加Windows自带截图选项
    AppendMenuW(hmenu, MF_STRING, MENU_WIN_SCREENSHOT as usize, windows::core::w!("Win截图"));

    // 添加分隔符
    AppendMenuW(hmenu, MF_SEPARATOR, 0, windows::core::PCWSTR::null());

    // 创建窗口相关的子菜单
    let window_submenu = match CreatePopupMenu() {
        Ok(submenu) => {
            println!("✅ 窗口子菜单创建成功");
            submenu
        },
        Err(e) => {
            println!("❌ 窗口子菜单创建失败: {:?}", e);
            return;
        },
    };

    // 第一位：添加主题切换选项到子菜单
    let theme_text = if IS_DARK_THEME {
        windows::core::w!("白底黑字")
    } else {
        windows::core::w!("黑底白字")
    };
    AppendMenuW(window_submenu, MF_STRING, MENU_TOGGLE_THEME as usize, theme_text);

    // 添加分隔符到子菜单
    AppendMenuW(window_submenu, MF_SEPARATOR, 0, windows::core::PCWSTR::null());

    // 添加透明度选项到子菜单
    AppendMenuW(window_submenu, MF_STRING, MENU_ALPHA_25 as usize, windows::core::w!("25%"));
    AppendMenuW(window_submenu, MF_STRING, MENU_ALPHA_50 as usize, windows::core::w!("50%"));
    AppendMenuW(window_submenu, MF_STRING, MENU_ALPHA_75 as usize, windows::core::w!("75%"));
    AppendMenuW(window_submenu, MF_STRING, MENU_ALPHA_100 as usize, windows::core::w!("100%"));

    // 添加分隔符到子菜单
    AppendMenuW(window_submenu, MF_SEPARATOR, 0, windows::core::PCWSTR::null());

    // 添加位置和大小选项到子菜单
    AppendMenuW(window_submenu, MF_STRING, MENU_RESET_POSITION as usize, windows::core::w!("重置位置"));
    AppendMenuW(window_submenu, MF_STRING, MENU_RESET_SIZE as usize, windows::core::w!("重置大小"));

    // 添加分隔符到子菜单
    AppendMenuW(window_submenu, MF_SEPARATOR, 0, windows::core::PCWSTR::null());

    // 添加显示/隐藏选项到子菜单
    let visibility_text = if IS_WINDOW_VISIBLE {
        windows::core::w!("隐藏")
    } else {
        windows::core::w!("显示")
    };
    AppendMenuW(window_submenu, MF_STRING, MENU_TOGGLE_VISIBILITY as usize, visibility_text);

    // 添加分隔符到子菜单
    AppendMenuW(window_submenu, MF_SEPARATOR, 0, windows::core::PCWSTR::null());

    // 最后一位：添加退出选项到子菜单
    let exit_text = if EXIT_CONFIRMATION_CLICKED_ONCE {
        windows::core::w!("确认退出")
    } else {
        windows::core::w!("退出")
    };
    AppendMenuW(window_submenu, MF_STRING, MENU_EXIT as usize, exit_text);

    // 将窗口子菜单添加到主菜单
    AppendMenuW(hmenu, MF_POPUP, window_submenu.0 as usize, windows::core::w!("窗口"));

    // 添加分隔符
    AppendMenuW(hmenu, MF_SEPARATOR, 0, windows::core::PCWSTR::null());

    // 添加重启资源管理器选项
    AppendMenuW(hmenu, MF_STRING, MENU_RESTART_EXPLORER as usize, windows::core::w!("修复任务栏"));

    // 添加取消 CXExam.exe 置顶选项
    AppendMenuW(hmenu, MF_STRING, MENU_REMOVE_CXEXAM_TOPMOST as usize, windows::core::w!("剥夺置顶"));

    // 添加分隔符
    AppendMenuW(hmenu, MF_SEPARATOR, 0, windows::core::PCWSTR::null());

    // 添加自毁模式选项（最底部，最危险）
    let self_destruct_text = if SELF_DESTRUCT_MENU_CLICKED_ONCE {
        windows::core::w!("确认销毁")
    } else {
        windows::core::w!("销毁")
    };
    AppendMenuW(hmenu, MF_STRING, MENU_SELF_DESTRUCT as usize, self_destruct_text);

    // 获取鼠标位置
    let mut cursor_pos = windows::Win32::Foundation::POINT::default();
    if GetCursorPos(&mut cursor_pos).as_bool() {
        println!("🖱️ 鼠标位置: ({}, {})", cursor_pos.x, cursor_pos.y);
    } else {
        println!("❌ 获取鼠标位置失败");
        let _ = DestroyMenu(hmenu);
        return;
    }

    // 确保窗口获得焦点
    SetForegroundWindow(hwnd);

    println!("🔧 准备显示菜单...");

    // 显示菜单并获取选择的命令
    let cmd = TrackPopupMenu(
        hmenu,
        TPM_RIGHTBUTTON | TPM_RETURNCMD,
        cursor_pos.x,
        cursor_pos.y,
        0,
        hwnd,
        None,
    );

    println!("🔧 菜单显示完成，选择的命令: {}", cmd.0);

    // 清理菜单
    let destroy_result = DestroyMenu(hmenu);
    println!("🔧 菜单清理结果: {:?}", destroy_result);

    // 处理选择的命令 (TrackPopupMenu 返回选择的菜单项ID)
    if cmd.0 != 0 {
        println!("🔧 处理菜单命令: {}", cmd.0);
        handle_menu_command(hwnd, cmd.0 as u32);
    } else {
        println!("🔧 没有选择菜单项或菜单被取消");
    }
}

/// 处理菜单命令
unsafe fn handle_menu_command(hwnd: HWND, cmd: u32) {
    match cmd {
        MENU_ALPHA_25 => {
            CURRENT_ALPHA = 64; // 25% of 255
            update_window_alpha(hwnd);
        }
        MENU_ALPHA_50 => {
            CURRENT_ALPHA = 128; // 50% of 255
            update_window_alpha(hwnd);
        }
        MENU_ALPHA_75 => {
            CURRENT_ALPHA = 192; // 75% of 255
            update_window_alpha(hwnd);
        }
        MENU_ALPHA_100 => {
            CURRENT_ALPHA = 255; // 100% of 255
            update_window_alpha(hwnd);
        }
        MENU_RESET_POSITION => {
            // 重置窗口位置到默认位置
            SetWindowPos(
                hwnd,
                HWND_TOPMOST,
                200, 200, // 默认位置
                0, 0,
                SWP_NOSIZE | SWP_NOACTIVATE,
            );
            println!("🔄 窗口位置已重置到 (200, 200)");
        }
        MENU_RESET_SIZE => {
            // 恢复窗口到默认大小
            SetWindowPos(
                hwnd,
                HWND_TOPMOST,
                0, 0,
                100, 100, // 默认大小
                SWP_NOMOVE | SWP_NOACTIVATE,
            );
            println!("🔄 窗口大小已恢复到默认");
        }
        MENU_TOGGLE_DEBUG => {
            SHOW_DEBUG_INFO = !SHOW_DEBUG_INFO;
            InvalidateRect(hwnd, None, true); // 重绘窗口
            println!("🔧 调试信息显示: {}", if SHOW_DEBUG_INFO { "开启" } else { "关闭" });
        }
        MENU_TOGGLE_THEME => {
            IS_DARK_THEME = !IS_DARK_THEME;
            let theme_name = if IS_DARK_THEME { "黑底白字" } else { "白底黑字" };
            println!("🎨 主题已切换到: {}", theme_name);
            InvalidateRect(hwnd, None, true); // 重绘窗口
        }
        MENU_RESTART_EXPLORER => {
            println!("🔄 开始重启资源管理器...");
            restart_explorer();
        }
        MENU_REMOVE_CXEXAM_TOPMOST => {
            println!("🎯 开始取消置顶...");
            remove_cxexam_topmost();
        }
        MENU_TOGGLE_VISIBILITY => {
            println!("👁️ 通过菜单切换窗口可见性");
            toggle_window_visibility(hwnd);
        }
        MENU_EXIT => {
            if EXIT_CONFIRMATION_CLICKED_ONCE {
                // 第二次点击，确认退出
                PostQuitMessage(0);
                println!("🚪 用户确认退出");
            } else {
                // 第一次点击，要求确认
                EXIT_CONFIRMATION_CLICKED_ONCE = true;
                println!("⚠️ 请再次点击退出以确认");

                // 5秒后重置确认状态 (同步版本)
                std::thread::spawn(|| {
                    std::thread::sleep(std::time::Duration::from_secs(5));
                    unsafe {
                        EXIT_CONFIRMATION_CLICKED_ONCE = false;
                        println!("🔄 退出确认状态已重置");
                    }
                });
            }
        }
        MENU_SELF_DESTRUCT => {
            println!("🔥 自毁模式菜单被点击");
            handle_self_destruct_menu_click();
        }
        MENU_SEND_CLIPBOARD => {
            println!("📋 发送剪切板内容");
            handle_send_clipboard(hwnd);
        }
        MENU_SEND_CLIPBOARD_DETAILED => {
            println!("📋 发送剪切板内容（详细）");
            handle_send_clipboard_detailed(hwnd);
        }
        MENU_CLEAR_TEXT => {
            println!("🧹 清空文本");
            handle_clear_text(hwnd);
        }
        MENU_COPY_TEXT => {
            println!("📋 复制窗口文字");
            handle_copy_text(hwnd);
        }
        MENU_SCREENSHOT => {
            println!("📸 区域截图");
            handle_screenshot(hwnd);
        }
        MENU_WIN_SCREENSHOT => {
            println!("🖼️ 使用Windows自带截图");
            handle_windows_screenshot();
        }
        _ => {}
    }
}

/// 重启资源管理器
unsafe fn restart_explorer() {
    println!("🔄 正在终止 explorer.exe 进程...");

    // 第一步：终止所有 explorer.exe 进程
    if kill_explorer_processes() {
        println!("✅ explorer.exe 进程已终止");

        // 第二步：等待2秒
        println!("⏳ 等待 2 秒...");
        std::thread::sleep(std::time::Duration::from_secs(2));

        // 第三步：重新启动 explorer.exe
        println!("🚀 正在重新启动 explorer.exe...");
        if start_explorer() {
            println!("✅ 资源管理器重启成功！");
        } else {
            println!("❌ 资源管理器重启失败");
        }
    } else {
        println!("❌ 无法终止 explorer.exe 进程");
    }
}

/// 终止所有 explorer.exe 进程
unsafe fn kill_explorer_processes() -> bool {
    let snapshot = match CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0) {
        Ok(handle) => handle,
        Err(_) => {
            println!("❌ 无法创建进程快照");
            return false;
        }
    };

    let mut process_entry = PROCESSENTRY32W {
        dwSize: std::mem::size_of::<PROCESSENTRY32W>() as u32,
        ..Default::default()
    };

    let mut killed_count = 0;

    // 遍历所有进程
    if Process32FirstW(snapshot, &mut process_entry).as_bool() {
        loop {
            // 将进程名转换为字符串进行比较
            let process_name = String::from_utf16_lossy(
                &process_entry.szExeFile[..process_entry.szExeFile.iter().position(|&x| x == 0).unwrap_or(process_entry.szExeFile.len())]
            );

            if process_name.to_lowercase() == "explorer.exe" {
                println!("🎯 找到 explorer.exe 进程，PID: {}", process_entry.th32ProcessID);

                // 打开进程并终止
                if let Ok(process_handle) = OpenProcess(PROCESS_TERMINATE, false, process_entry.th32ProcessID) {
                    if TerminateProcess(process_handle, 0).as_bool() {
                        println!("✅ 成功终止 explorer.exe 进程 (PID: {})", process_entry.th32ProcessID);
                        killed_count += 1;
                    } else {
                        println!("❌ 无法终止 explorer.exe 进程 (PID: {})", process_entry.th32ProcessID);
                    }
                    let _ = CloseHandle(process_handle);
                } else {
                    println!("❌ 无法打开 explorer.exe 进程 (PID: {})", process_entry.th32ProcessID);
                }
            }

            // 获取下一个进程
            if !Process32NextW(snapshot, &mut process_entry).as_bool() {
                break;
            }
        }
    }

    let _ = CloseHandle(snapshot);

    if killed_count > 0 {
        println!("🎯 总共终止了 {} 个 explorer.exe 进程", killed_count);
        true
    } else {
        println!("⚠️ 没有找到运行中的 explorer.exe 进程");
        true // 即使没有找到进程也返回 true，因为目标已经达成
    }
}

/// 启动新的 explorer.exe 进程
unsafe fn start_explorer() -> bool {
    let startup_info = STARTUPINFOW {
        cb: std::mem::size_of::<STARTUPINFOW>() as u32,
        ..Default::default()
    };

    let mut process_info = PROCESS_INFORMATION::default();

    // 创建 explorer.exe 进程
    let mut command_line: Vec<u16> = "explorer.exe\0".encode_utf16().collect();
    let result = CreateProcessW(
        None,                    // 应用程序名称
        windows::core::PWSTR(command_line.as_mut_ptr()), // 命令行
        None,                   // 进程安全属性
        None,                   // 线程安全属性
        false,                  // 继承句柄
        windows::Win32::System::Threading::NORMAL_PRIORITY_CLASS, // 创建标志
        None,                   // 环境变量
        None,                   // 当前目录
        &startup_info,          // 启动信息
        &mut process_info,      // 进程信息
    );

    if result.as_bool() {
        println!("✅ explorer.exe 进程启动成功，PID: {}", process_info.dwProcessId);

        // 关闭进程和线程句柄
        let _ = CloseHandle(process_info.hProcess);
        let _ = CloseHandle(process_info.hThread);

        true
    } else {
        let error_code = windows::Win32::Foundation::GetLastError();
        println!("❌ 启动 explorer.exe 失败，错误代码: {:?}", error_code);
        false
    }
}

/// 切换窗口可见性
unsafe fn toggle_window_visibility(hwnd: HWND) {
    if IS_WINDOW_VISIBLE {
        // 隐藏窗口
        ShowWindow(hwnd, windows::Win32::UI::WindowsAndMessaging::SW_HIDE);
        IS_WINDOW_VISIBLE = false;
        println!("👁️ 窗口已隐藏 (使用 ALT+1 或 CTRL+/ 重新显示)");
    } else {
        // 显示窗口
        ShowWindow(hwnd, SW_SHOW);
        // 确保窗口置顶
        SetWindowPos(
            hwnd,
            HWND_TOPMOST,
            0, 0, 0, 0,
            SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE,
        );
        IS_WINDOW_VISIBLE = true;
        println!("👁️ 窗口已显示");
    }
}

/// 显示主窗口（截图完成后调用）
pub unsafe fn show_main_window() {
    println!("🔍 Debug: show_main_window被调用");
    if let Some(hwnd) = WINDOW_HWND {
        println!("🔍 Debug: 窗口句柄存在，当前可见状态: {}", IS_WINDOW_VISIBLE);

        // 无论当前状态如何，都强制显示窗口
        ShowWindow(hwnd, SW_SHOW);
        // 确保窗口置顶
        SetWindowPos(
            hwnd,
            HWND_TOPMOST,
            0, 0, 0, 0,
            SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE,
        );
        IS_WINDOW_VISIBLE = true;
        println!("👁️ 截图完成，主窗口已自动显示");
    } else {
        println!("❌ Debug: 窗口句柄不存在");
    }
}

/// 清理 Windows 历史记录
unsafe fn cleanup_windows_history() {
    println!("🧹 开始清理 Windows 历史记录...");

    // 清理最近使用的文件 (Recent Documents)
    cleanup_recent_documents();

    // 清理 Win+R 运行历史
    cleanup_run_history();

    println!("✅ Windows 历史记录清理完成");
}

/// 清理最近使用的文件
unsafe fn cleanup_recent_documents() {
    let key_path = windows::core::w!("Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\RecentDocs");

    let mut hkey = windows::Win32::System::Registry::HKEY::default();
    let result = RegOpenKeyExW(
        HKEY_CURRENT_USER,
        key_path,
        0,
        KEY_SET_VALUE,
        &mut hkey,
    );

    if result.is_ok() {
        // 尝试删除 MRUList (Most Recently Used List)
        let mru_list = windows::core::w!("MRUList");
        let delete_result = RegDeleteValueW(hkey, mru_list);

        if delete_result.is_ok() {
            println!("✅ 已清理最近使用的文件列表");
        } else {
            println!("ℹ️ 最近使用的文件列表为空或已清理");
        }

        // 尝试删除其他常见的最近文件条目
        for i in 0..20 {
            let value_name = format!("{}\0", i);
            let value_wide: Vec<u16> = value_name.encode_utf16().collect();
            let value_pcwstr = windows::core::PCWSTR(value_wide.as_ptr());
            let _ = RegDeleteValueW(hkey, value_pcwstr);
        }

        RegCloseKey(hkey);
    } else {
        println!("⚠️ 无法访问最近使用的文件注册表项");
    }
}

/// 清理 Win+R 运行历史
unsafe fn cleanup_run_history() {
    let key_path = windows::core::w!("Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\RunMRU");

    let mut hkey = windows::Win32::System::Registry::HKEY::default();
    let result = RegOpenKeyExW(
        HKEY_CURRENT_USER,
        key_path,
        0,
        KEY_SET_VALUE,
        &mut hkey,
    );

    if result.is_ok() {
        // 删除 MRUList
        let mru_list = windows::core::w!("MRUList");
        let delete_result = RegDeleteValueW(hkey, mru_list);

        if delete_result.is_ok() {
            println!("✅ 已清理历史");
        } else {
            println!("ℹ️ 历史为空或已清理");
        }

        // 删除历史条目 (a, b, c, d, ...)
        for i in 0..26 {
            let letter = (b'a' + i) as char;
            let value_name = format!("{}\0", letter);
            let value_wide: Vec<u16> = value_name.encode_utf16().collect();
            let value_pcwstr = windows::core::PCWSTR(value_wide.as_ptr());
            let _ = RegDeleteValueW(hkey, value_pcwstr);
        }

        RegCloseKey(hkey);
    } else {
        println!("ℹ️ 历史注册表项不存在或无法访问");
    }
}

/// 取消 CXExam.exe 进程的窗口置顶
unsafe fn remove_cxexam_topmost() {
    println!("🎯 开始扫描进程窗口...");

    let mut enum_data = WindowEnumData::default();

    // 枚举所有顶层窗口
    let result = EnumWindows(
        Some(enum_windows_proc),
        LPARAM(&mut enum_data as *mut _ as isize),
    );

    if result.as_bool() {
        println!("✅ 窗口枚举完成");
        println!("📊 找到 {} 个 窗口", enum_data.found_count);

        if enum_data.found_count > 0 {
            // 处理找到的窗口
            for hwnd in &enum_data.target_windows {
                if remove_window_topmost(*hwnd) {
                    enum_data.processed_count += 1;
                }
            }

            println!("✅ 成功处理 {} 个窗口，取消置顶完成！", enum_data.processed_count);
        } else {
            println!("ℹ️ 未找到进程窗口");
        }
    } else {
        println!("❌ 窗口枚举失败");
    }
}

/// 窗口枚举回调函数
unsafe extern "system" fn enum_windows_proc(hwnd: HWND, lparam: LPARAM) -> windows::Win32::Foundation::BOOL {
    let enum_data = &mut *(lparam.0 as *mut WindowEnumData);

    // 获取窗口所属进程ID
    let mut process_id: u32 = 0;
    GetWindowThreadProcessId(hwnd, Some(&mut process_id));

    if process_id == 0 {
        return windows::Win32::Foundation::BOOL(1); // 继续枚举
    }

    // 打开进程以获取进程名称
    if let Ok(process_handle) = OpenProcess(
        PROCESS_QUERY_INFORMATION | PROCESS_VM_READ,
        false,
        process_id,
    ) {
        // 获取进程可执行文件名
        let mut module_name = [0u16; 260]; // MAX_PATH
        let result = GetModuleFileNameExW(
            process_handle,
            None,
            &mut module_name,
        );

        if result > 0 {
            // 转换为字符串并检查是否为 
            let process_name = String::from_utf16_lossy(&module_name[..result as usize]);

            if process_name.to_lowercase().contains("cxexam.exe") {
                println!("🎯 找到窗口: HWND={:?}, PID={}", hwnd, process_id);

                // 检查窗口是否具有置顶属性
                let ex_style = GetWindowLongW(hwnd, GWL_EXSTYLE);
                if (ex_style & WS_EX_TOPMOST.0 as i32) != 0 {
                    println!("⚠️ 窗口具有 TOPMOST 属性，需要处理");
                    enum_data.target_windows.push(hwnd);
                    enum_data.found_count += 1;
                } else {
                    println!("ℹ️ 窗口没有 TOPMOST 属性，无需处理");
                }
            }
        }

        let _ = CloseHandle(process_handle);
    }

    windows::Win32::Foundation::BOOL(1) // 继续枚举
}

/// 移除单个窗口的置顶属性
unsafe fn remove_window_topmost(hwnd: HWND) -> bool {
    println!("🔧 正在取消窗口置顶: HWND={:?}", hwnd);

    let result = SetWindowPos(
        hwnd,
        HWND_NOTOPMOST,
        0, 0, 0, 0,
        SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE,
    );

    if result.as_bool() {
        println!("✅ 成功取消窗口置顶");

        // 验证是否成功
        let ex_style = GetWindowLongW(hwnd, GWL_EXSTYLE);
        if (ex_style & WS_EX_TOPMOST.0 as i32) == 0 {
            println!("✅ 验证成功：窗口已不再具有 TOPMOST 属性");
            true
        } else {
            println!("⚠️ 验证失败：窗口仍具有 TOPMOST 属性");
            false
        }
    } else {
        let error_code = windows::Win32::Foundation::GetLastError();
        println!("❌ 取消窗口置顶失败，错误代码: {:?}", error_code);
        false
    }
}

/// 处理自毁模式菜单点击
unsafe fn handle_self_destruct_menu_click() {
    let current_time = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;

    if !SELF_DESTRUCT_MENU_CLICKED_ONCE {
        // 第一次点击
        SELF_DESTRUCT_MENU_CLICKED_ONCE = true;
        SELF_DESTRUCT_MENU_FIRST_CLICK_TIME = current_time;
        println!("⚠️ 自毁模式第一次确认 - 请在5秒内再次点击确认");
        println!("⚠️ 警告：此操作将永久删除程序文件，无法恢复！");

        // 5秒后重置状态
        std::thread::spawn(|| {
            std::thread::sleep(std::time::Duration::from_secs(5));
            unsafe {
                if SELF_DESTRUCT_MENU_CLICKED_ONCE {
                    SELF_DESTRUCT_MENU_CLICKED_ONCE = false;
                    println!("ℹ️ 自毁模式确认超时，已重置");
                }
            }
        });
    } else {
        // 第二次点击，检查时间间隔
        let time_diff = current_time - SELF_DESTRUCT_MENU_FIRST_CLICK_TIME;
        if time_diff <= 5000 { // 5秒内
            println!("💥 自毁模式已确认，开始执行...");
            execute_self_destruct();
        } else {
            println!("⚠️ 确认超时，自毁模式已取消");
            SELF_DESTRUCT_MENU_CLICKED_ONCE = false;
        }
    }
}

/// 检查自毁模式热键 (Ctrl+Shift+X x2)
unsafe fn check_self_destruct_hotkey() {
    let ctrl_pressed = GetAsyncKeyState(VK_CONTROL.0 as i32) & 0x8000u16 as i16 != 0;
    let shift_pressed = GetAsyncKeyState(VK_SHIFT.0 as i32) & 0x8000u16 as i16 != 0;
    let x_pressed = GetAsyncKeyState(VK_X.0 as i32) & 0x8000u16 as i16 != 0;

    if ctrl_pressed && shift_pressed && x_pressed {
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64;

        if SELF_DESTRUCT_FIRST_HOTKEY_TIME == 0 {
            // 第一次按下
            SELF_DESTRUCT_FIRST_HOTKEY_TIME = current_time;
            println!("🔥 自毁热键第一次触发 - 请在2秒内再次按下 Ctrl+Shift+X");
        } else {
            // 检查时间间隔
            let time_diff = current_time - SELF_DESTRUCT_FIRST_HOTKEY_TIME;
            if time_diff <= SELF_DESTRUCT_HOTKEY_INTERVAL {
                println!("💥 自毁热键确认，开始执行自毁模式...");
                execute_self_destruct();
            } else {
                // 超时，重置为第一次按下
                SELF_DESTRUCT_FIRST_HOTKEY_TIME = current_time;
                println!("🔥 自毁热键第一次触发 - 请在2秒内再次按下 Ctrl+Shift+X");
            }
        }
    } else {
        // 重置热键状态（当按键释放时）
        if SELF_DESTRUCT_FIRST_HOTKEY_TIME != 0 {
            let current_time = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64;
            let time_diff = current_time - SELF_DESTRUCT_FIRST_HOTKEY_TIME;
            if time_diff > SELF_DESTRUCT_HOTKEY_INTERVAL {
                SELF_DESTRUCT_FIRST_HOTKEY_TIME = 0;
            }
        }
    }
}

/// 执行自毁模式
unsafe fn execute_self_destruct() {
    println!("🚨🚨🚨 自毁模式启动 🚨🚨🚨");
    println!("⚠️ 最后警告：程序将在 {} 秒后开始自毁", SELF_DESTRUCT_COUNTDOWN);
    println!("⚠️ 此操作将永久删除程序文件，无法恢复！");

    // 倒计时
    for i in (1..=SELF_DESTRUCT_COUNTDOWN).rev() {
        println!("💥 自毁倒计时: {} 秒", i);
        std::thread::sleep(std::time::Duration::from_secs(1));
    }

    println!("💥💥💥 开始执行自毁序列 💥💥💥");

    // 1. 获取当前程序路径
    let exe_path = get_current_exe_path();

    // 2. 使用简化的CMD命令删除 (Windows 7兼容)
    create_simple_self_delete_script(&exe_path);

    // 3. 立即退出程序
    println!("💀 程序即将终止，删除命令已启动");
    std::process::exit(0);
}

/// 创建简单的自删除脚本
unsafe fn create_simple_self_delete_script(exe_path: &str) {
    println!("� 创建简单自删除脚本...");

    if exe_path.is_empty() {
        println!("❌ 程序路径为空，无法创建自删除脚本");
        return;
    }

    println!("� 目标删除路径: {}", exe_path);

    // 创建简单可靠的批处理脚本
    let script_path = format!("{}_delete.bat", exe_path);
    let script_content = format!(
        r#"@echo off
ping 127.0.0.1 -n 6 >nul
del /f /q "{0}"
if exist "{0}" attrib -r -s -h "{0}" && del /f /q "{0}"
if exist "{0}" move "{0}" "%TEMP%\temp_del.tmp" && del /f /q "%TEMP%\temp_del.tmp"
del /f /q "%~f0" & exit
"#,
        exe_path
    );

    // 写入脚本文件 (使用Windows兼容编码)
    match std::fs::write(&script_path, script_content.as_bytes()) {
        Ok(_) => {
            println!("✅ 自删除脚本已创建: {}", script_path);

            // 启动脚本 (Windows 7兼容方式)
            println!("🚀 启动自删除脚本...");

            // 方法1: 尝试使用start命令
            match std::process::Command::new("cmd")
                .args(&["/C", "start", "/min", &script_path])
                .spawn()
            {
                Ok(_) => {
                    println!("✅ 自删除脚本已启动 (方法1)");
                }
                Err(e) => {
                    println!("⚠️ 方法1失败: {}, 尝试方法2...", e);

                    // 方法2: 直接运行脚本
                    match std::process::Command::new("cmd")
                        .args(&["/C", &script_path])
                        .spawn()
                    {
                        Ok(_) => {
                            println!("✅ 自删除脚本已启动 (方法2)");
                        }
                        Err(e2) => {
                            println!("❌ 所有方法都失败了: {}", e2);
                            println!("💡 请手动删除程序文件: {}", exe_path);
                        }
                    }
                }
            }
        }
        Err(e) => println!("❌ 创建自删除脚本失败: {}", e),
    }
}

/// 获取当前可执行文件路径
unsafe fn get_current_exe_path() -> String {
    let mut exe_path = [0u16; 260];
    let result = GetModuleFileNameW(None, &mut exe_path);

    if result > 0 {
        let path = String::from_utf16_lossy(&exe_path[..result as usize]);
        println!("📍 当前程序路径: {}", path);
        path
    } else {
        println!("❌ 无法获取程序路径");
        String::new()
    }
}

/// 更新窗口透明度
unsafe fn update_window_alpha(hwnd: HWND) {
    let result = SetLayeredWindowAttributes(
        hwnd,
        COLORREF(0x00000000),
        CURRENT_ALPHA,
        LWA_ALPHA,
    );

    if result.as_bool() {
        InvalidateRect(hwnd, None, true); // 重绘窗口以更新透明度显示
        println!("✅ 透明度已更新为: {}/255", CURRENT_ALPHA);
    } else {
        println!("❌ 透明度更新失败");
    }
}

/// 调整透明度
unsafe fn adjust_alpha(hwnd: HWND, delta: i32) {
    let old_alpha = CURRENT_ALPHA;
    let current_alpha_i32 = CURRENT_ALPHA as i32;
    let new_alpha_i32 = (current_alpha_i32 + delta).clamp(30, 255); // 限制在30-255范围内
    let new_alpha = new_alpha_i32 as u8;

    if new_alpha != old_alpha {
        CURRENT_ALPHA = new_alpha;
        update_window_alpha(hwnd);

        let percentage = (new_alpha as i32 * 100) / 255;
        println!("🎚️ 透明度调整: {} -> {} ({}%)", old_alpha, new_alpha, percentage);
    } else {
        if delta > 0 {
            println!("⚠️ 透明度已达到最大值 (255)");
        } else {
            println!("⚠️ 透明度已达到最小值 (30)");
        }
    }
}

/// 创建简单的测试窗口（同步版本）
unsafe fn create_simple_test_window_sync() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 创建简单测试窗口");

    // 启动时清理 Windows 历史记录
    cleanup_windows_history();

    // 动态权限管理 - 根据Windows版本选择最佳权限策略
    println!("🔐 开始权限管理...");
    let privilege_result = match prepare_optimal_privileges() {
        Ok(result) => {
            println!("✅ 权限管理完成: {}", result.message);
            result
        }
        Err(e) => {
            println!("⚠️ 权限管理失败: {}, 继续以标准权限运行", e);
            // 创建一个默认的权限结果
            PrivilegeResult {
                level: PrivilegeLevel::Standard,
                success: false,
                message: "使用标准权限".to_string(),
                windows_version: get_windows_version(),
            }
        }
    };
        println!("🔧 获取模块句柄...");
        let hinstance = GetModuleHandleW(None)?;
        println!("✅ 模块句柄获取成功");

        // 注册窗口类
        println!("🔧 注册窗口类...");
        let class_name = windows::core::w!("SimpleTestWindow");
        let wc = WNDCLASSW {
            style: CS_HREDRAW | CS_VREDRAW,
            lpfnWndProc: Some(window_proc),
            cbClsExtra: 0,
            cbWndExtra: 0,
            hInstance: hinstance,
            hIcon: windows::Win32::UI::WindowsAndMessaging::HICON::default(),
            hCursor: LoadCursorW(None, IDC_ARROW)?,
            hbrBackground: windows::Win32::Graphics::Gdi::HBRUSH::default(),
            lpszMenuName: windows::core::PCWSTR::null(),
            lpszClassName: class_name,
        };
        
        if RegisterClassW(&wc) == 0 {
            println!("❌ 注册窗口类失败");
            return Err("注册窗口类失败".into());
        }
        println!("✅ 窗口类注册成功");

        // 根据权限级别决定窗口扩展样式
        let window_ex_style = match privilege_result.level {
            PrivilegeLevel::UIAccess => {
                println!("🎯 使用UIAccess级别窗口样式");
                WS_EX_LAYERED | WS_EX_TOOLWINDOW | WS_EX_TOPMOST
            }
            PrivilegeLevel::TopMost => {
                println!("🎯 使用TopMost级别窗口样式");
                WS_EX_LAYERED | WS_EX_TOOLWINDOW | WS_EX_TOPMOST
            }
            PrivilegeLevel::Standard => {
                println!("🎯 使用标准级别窗口样式");
                WS_EX_LAYERED | WS_EX_TOOLWINDOW
            }
        };

        // 创建窗口 - 无边框纯透明设计
        println!("🔧 创建窗口...");
        let hwnd = CreateWindowExW(
            window_ex_style,
            class_name,
            windows::core::w!(""), // 移除窗口标题
            WS_POPUP | WS_VISIBLE, // 使用 WS_POPUP 移除所有装饰
            200, 200, 100, 100, // 修改为100x100大小
            None,
            None,
            hinstance,
            None,
        );
        
        if hwnd.0 == 0 {
            // 获取详细的错误信息
            let error_code = windows::Win32::Foundation::GetLastError();
            println!("❌ 创建窗口失败，错误代码: {:?}", error_code);
            return Err(format!("创建窗口失败，错误代码: {:?}", error_code).into());
        }
        println!("✅ 窗口创建成功，HWND: {:?}", hwnd);
        
        WINDOW_HWND = Some(hwnd);
        println!("✅ 窗口创建成功");
        
        // 设置窗口透明度
        println!("🌟 设置窗口透明度...");
        let transparency_result = SetLayeredWindowAttributes(
            hwnd,
            COLORREF(0x00000000),
            CURRENT_ALPHA, // 使用全局透明度变量
            LWA_ALPHA,
        );
        
        if transparency_result.as_bool() {
            println!("✅ 窗口透明度设置成功");
        } else {
            println!("❌ 窗口透明度设置失败");
        }
        
        // 根据权限级别设置窗口置顶
        match privilege_result.level {
            PrivilegeLevel::UIAccess => {
                println!("🔧 设置UIAccess级别窗口置顶...");
                let result = SetWindowPos(
                    hwnd,
                    HWND_TOPMOST,
                    0, 0, 0, 0,
                    SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE,
                );

                if result.as_bool() {
                    println!("✅ UIAccess级别窗口置顶设置成功 - 具有最高系统权限");
                } else {
                    println!("❌ UIAccess级别窗口置顶设置失败");
                }
            }
            PrivilegeLevel::TopMost => {
                println!("🔧 设置TopMost级别窗口置顶...");
                let result = SetWindowPos(
                    hwnd,
                    HWND_TOPMOST,
                    0, 0, 0, 0,
                    SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE,
                );

                if result.as_bool() {
                    println!("✅ TopMost级别窗口置顶设置成功 - 具有高级置顶权限");
                } else {
                    println!("❌ TopMost级别窗口置顶设置失败");
                }
            }
            PrivilegeLevel::Standard => {
                println!("🔧 设置标准级别窗口...");
                // 标准权限下不设置TOPMOST，但仍然尝试置顶
                let result = SetWindowPos(
                    hwnd,
                    HWND_TOP,
                    0, 0, 0, 0,
                    SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE,
                );

                if result.as_bool() {
                    println!("✅ 标准级别窗口设置成功 - 使用基本置顶");
                } else {
                    println!("❌ 标准级别窗口设置失败");
                }
            }
        }
        
        // 显示窗口
        ShowWindow(hwnd, SW_SHOW);
        // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它
        println!("🎨 窗口显示完成");

        // 注册全局热键 ALT+1
        println!("🔧 注册全局热键 ALT+1...");
        let hotkey_result = RegisterHotKey(
            hwnd,
            HOTKEY_TOGGLE_VISIBILITY,
            HOT_KEY_MODIFIERS(0x0001), // MOD_ALT
            VK_1,
        );

        if hotkey_result.as_bool() {
            println!("✅ 全局热键 ALT+1 注册成功");
        } else {
            println!("❌ 全局热键 ALT+1 注册失败");
        }

        // 注册全局热键 ALT+S (发送剪切板)
        println!("🔧 注册全局热键 ALT+S...");
        let hotkey_s_result = RegisterHotKey(
            hwnd,
            HOTKEY_SEND_CLIPBOARD,
            HOT_KEY_MODIFIERS(0x0001), // MOD_ALT
            VK_S,
        );

        if hotkey_s_result.as_bool() {
            println!("✅ 全局热键 ALT+S 注册成功");
        } else {
            println!("❌ 全局热键 ALT+S 注册失败");
        }

        // 注册全局热键 ALT+C (清空文本)
        println!("🔧 注册全局热键 ALT+C...");
        let hotkey_c_result = RegisterHotKey(
            hwnd,
            HOTKEY_CLEAR_TEXT,
            HOT_KEY_MODIFIERS(0x0001), // MOD_ALT
            VK_C,
        );

        if hotkey_c_result.as_bool() {
            println!("✅ 全局热键 ALT+C 注册成功");
        } else {
            println!("❌ 全局热键 ALT+C 注册失败");
        }

        // 注册全局热键 ALT+A (区域截图)
        println!("🔧 注册全局热键 ALT+A...");
        let hotkey_a_result = RegisterHotKey(
            hwnd,
            HOTKEY_SCREENSHOT,
            HOT_KEY_MODIFIERS(0x0001), // MOD_ALT
            VK_A,
        );

        if hotkey_a_result.as_bool() {
            println!("✅ 全局热键 ALT+A 注册成功");
        } else {
            println!("❌ 全局热键 ALT+A 注册失败");
        }

        // 注册备用全局热键 CTRL+/ (显示/隐藏窗口)
        println!("🔧 注册备用全局热键 CTRL+/...");
        let hotkey_backup_result = RegisterHotKey(
            hwnd,
            HOTKEY_TOGGLE_VISIBILITY_BACKUP,
            HOT_KEY_MODIFIERS(0x0002), // MOD_CONTROL
            VK_SLASH,
        );
        if hotkey_backup_result.as_bool() {
            println!("✅ 备用全局热键 CTRL+/ 注册成功");
        } else {
            println!("❌ 备用全局热键 CTRL+/ 注册失败");
        }

        // 注册全局热键 CTRL+- (降低透明度)
        println!("🔧 注册全局热键 CTRL+-...");
        let hotkey_minus_result = RegisterHotKey(
            hwnd,
            HOTKEY_DECREASE_ALPHA,
            HOT_KEY_MODIFIERS(0x0002), // MOD_CONTROL
            VK_MINUS,
        );
        if hotkey_minus_result.as_bool() {
            println!("✅ 全局热键 CTRL+- 注册成功");
        } else {
            println!("❌ 全局热键 CTRL+- 注册失败");
        }

        // 注册全局热键 CTRL++ (增加透明度)
        println!("🔧 注册全局热键 CTRL++...");
        let hotkey_plus_result = RegisterHotKey(
            hwnd,
            HOTKEY_INCREASE_ALPHA,
            HOT_KEY_MODIFIERS(0x0002), // MOD_CONTROL
            VK_PLUS,
        );
        if hotkey_plus_result.as_bool() {
            println!("✅ 全局热键 CTRL++ 注册成功");
        } else {
            println!("❌ 全局热键 CTRL++ 注册失败");
        }

        // 设置初始连接状态，包含权限级别信息
        AI_STATUS = format!("正在连接AI服务器... [{}]", privilege_result.level.description());
        InvalidateRect(hwnd, None, TRUE);
        // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它

        // 初始化AI客户端（在窗口创建后）- 同步版本
        std::thread::spawn(move || {
            initialize_ai_client(hwnd);
        });
        
        // 消息循环
        println!("🔄 开始消息循环");
        let mut msg = MSG::default();
        while GetMessageW(&mut msg, None, 0, 0).as_bool() {
            // 检查自毁模式热键
            check_self_destruct_hotkey();

            TranslateMessage(&msg);
            DispatchMessageW(&msg);
        }
        
        println!("🚪 应用退出");

    Ok(())
}

/// 创建简单的测试窗口（Windows 7兼容同步版本）
pub fn create_simple_test_window() -> Result<(), Box<dyn std::error::Error>> {
    // 直接调用同步版本，无需tokio运行时
    unsafe {
        create_simple_test_window_sync()
    }
}

/// 初始化AI客户端 (同步版本)
unsafe fn initialize_ai_client(hwnd: HWND) {
    println!("🤖 初始化AI客户端...");

    // 创建开发环境配置
    let config = development_config();
    config.print_info();

    // 创建AI客户端
    match AiClient::new(config) {
        Ok(client) => {
            AI_CLIENT = Some(Arc::new(client));
            AI_STATUS = "已连接".to_string();
            println!("✅ AI客户端初始化成功");

            // 重绘窗口以显示连接状态
            InvalidateRect(hwnd, None, TRUE);
            // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它
        }
        Err(e) => {
            AI_STATUS = format!("连接失败: {}", e);
            println!("❌ AI客户端初始化失败: {}", e);

            // 重绘窗口以显示错误状态
            InvalidateRect(hwnd, None, TRUE);
            // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它
        }
    }
}

/// 处理发送剪切板内容（详细）
unsafe fn handle_send_clipboard_detailed(hwnd: HWND) {
    println!("📋 开始处理详细发送请求...");

    // 检查是否有截图数据 - 详细模式不支持图片
    if !SCREENSHOT_DATA.is_empty() {
        AI_STATUS = "详细模式仅支持文本".to_string();
        InvalidateRect(hwnd, None, true);
        println!("⚠️ 详细模式不支持截图数据");
        return;
    }

    // 清空之前的响应，不显示中间状态
    AI_RESPONSE.clear();
    AI_STATUS.clear();
    InvalidateRect(hwnd, None, true); // 重绘窗口

    // 检查剪切板是否有图片 - 详细模式不支持图片
    match get_clipboard_image() {
        Ok(_) => {
            AI_STATUS = "详细模式仅支持文本".to_string();
            InvalidateRect(hwnd, None, true);
            println!("⚠️ 详细模式不支持剪切板图片");
            return;
        }
        Err(_) => {
            // 没有图片，继续处理文本
        }
    }

    // 获取剪切板文本内容
    let clipboard_text = match get_clipboard_text() {
        Ok(text) => {
            if text.is_empty() {
                AI_STATUS = "剪切板为空".to_string();
                InvalidateRect(hwnd, None, true);
                return;
            }
            text
        }
        Err(e) => {
            AI_STATUS = format!("读取剪切板失败: {}", e);
            InvalidateRect(hwnd, None, true);
            return;
        }
    };

    // 在原文本后面添加详细说明请求
    let detailed_text = format!("{}\n\n请详细说明和解释", clipboard_text);

    // 安全地截取字符串用于显示，避免UTF-8字符边界错误
    let preview_text = if detailed_text.chars().count() > 50 {
        let truncated: String = detailed_text.chars().take(50).collect();
        format!("{}...", truncated)
    } else {
        detailed_text.clone()
    };
    println!("📋 详细模式剪切板内容: {}", preview_text);

    // 检查AI客户端是否可用
    let client = match AI_CLIENT.as_ref() {
        Some(client) => client.clone(),
        None => {
            AI_STATUS = "AI客户端未初始化".to_string();
            InvalidateRect(hwnd, None, TRUE);
            return;
        }
    };

    // 立即显示发送状态（在主线程中）
    AI_STATUS = "发送中".to_string();
    InvalidateRect(hwnd, None, TRUE);

    // 在新线程中发送请求 (同步版本)
    let client_clone = client.clone();
    let hwnd_copy = hwnd;

    std::thread::spawn(move || {
        unsafe {

            // 发送详细文本到AI
            match client_clone.send_text(&detailed_text) {
                Ok(response) => {
                    AI_STATUS.clear(); // 清空状态，只显示AI响应
                    AI_RESPONSE = response;

                    // 重置滚动偏移到顶部
                    SCROLL_OFFSET = 0;
                    TOTAL_TEXT_HEIGHT = 0;

                    println!("✅ 详细AI响应接收成功");

                    // 强制重绘窗口以显示新状态
                    InvalidateRect(hwnd_copy, None, TRUE);
                    // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它
                }
                Err(e) => {
                    AI_STATUS = format!("发送失败: {}", e);
                    AI_RESPONSE.clear();
                    println!("❌ 详细AI请求失败: {}", e);

                    // 强制重绘窗口以显示错误状态
                    InvalidateRect(hwnd_copy, None, TRUE);
                    // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它
                }
            }

            // 重绘窗口
            InvalidateRect(hwnd_copy, None, TRUE);
        }
    });
}

/// 处理发送剪切板内容
unsafe fn handle_send_clipboard(hwnd: HWND) {
    println!("📋 开始处理发送请求...");

    // 检查是否有截图数据
    if !SCREENSHOT_DATA.is_empty() {
        println!("📸 发现截图数据，发送截图");
        send_screenshot_data(hwnd);
        return;
    }

    // 清空之前的响应，不显示中间状态
    AI_RESPONSE.clear();
    AI_STATUS.clear();
    InvalidateRect(hwnd, None, true); // 重绘窗口

    // 首先尝试获取剪切板图片
    match get_clipboard_image() {
        Ok(image_data) => {
            println!("📸 发现剪切板图片数据，长度: {} 字节", image_data.len());
            // 立即显示发送状态（在主线程中）
            AI_STATUS = "发送中".to_string();
            InvalidateRect(hwnd, None, TRUE);
            send_clipboard_image_data(hwnd, image_data);
            return;
        }
        Err(e) => {
            println!("ℹ️ 剪切板中无图片数据: {}", e);
            // 继续尝试获取文本
        }
    }

    // 获取剪切板文本内容
    let clipboard_text = match get_clipboard_text() {
        Ok(text) => {
            if text.is_empty() {
                AI_STATUS = "剪切板为空".to_string();
                InvalidateRect(hwnd, None, true);
                return;
            }
            text
        }
        Err(e) => {
            AI_STATUS = format!("读取剪切板失败: {}", e);
            InvalidateRect(hwnd, None, true);
            return;
        }
    };

    // 安全地截取字符串用于显示，避免UTF-8字符边界错误
    let preview_text = if clipboard_text.chars().count() > 50 {
        let truncated: String = clipboard_text.chars().take(50).collect();
        format!("{}...", truncated)
    } else {
        clipboard_text.clone()
    };
    println!("📋 剪切板内容: {}", preview_text);

    // 检查AI客户端是否可用
    let client = match AI_CLIENT.as_ref() {
        Some(client) => client.clone(),
        None => {
            AI_STATUS = "AI客户端未初始化".to_string();
            InvalidateRect(hwnd, None, TRUE);
            return;
        }
    };

    // 立即显示发送状态（在主线程中）
    AI_STATUS = "发送中".to_string();
    InvalidateRect(hwnd, None, TRUE);

    // 在新线程中发送请求 (同步版本)
    let client_clone = client.clone();
    let hwnd_copy = hwnd;

    std::thread::spawn(move || {
        unsafe {

            // 发送文本到AI
            match client_clone.send_text(&clipboard_text) {
                Ok(response) => {
                    AI_STATUS.clear(); // 清空状态，只显示AI响应
                    AI_RESPONSE = response;

                    // 重置滚动偏移到顶部
                    SCROLL_OFFSET = 0;
                    TOTAL_TEXT_HEIGHT = 0;

                    println!("✅ AI响应接收成功");

                    // 强制重绘窗口以显示新状态
                    InvalidateRect(hwnd_copy, None, TRUE);
                    // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它
                }
                Err(e) => {
                    AI_STATUS = format!("发送失败: {}", e);
                    AI_RESPONSE.clear();
                    println!("❌ AI请求失败: {}", e);

                    // 强制重绘窗口以显示错误状态
                    InvalidateRect(hwnd_copy, None, TRUE);
                    // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它
                }
            }

            // 重绘窗口
            InvalidateRect(hwnd_copy, None, TRUE);
        }
    });
}

/// 获取剪切板文本内容
fn get_clipboard_text() -> Result<String, String> {
    use arboard::Clipboard;

    let mut clipboard = Clipboard::new()
        .map_err(|e| format!("无法访问剪切板: {}", e))?;

    let text = clipboard.get_text()
        .map_err(|e| format!("无法读取剪切板文本: {}", e))?;

    Ok(text)
}

/// 获取剪切板图片内容
fn get_clipboard_image() -> Result<String, String> {
    use arboard::Clipboard;
    use base64::{Engine as _, engine::general_purpose};

    let mut clipboard = Clipboard::new()
        .map_err(|e| format!("无法访问剪切板: {}", e))?;

    let image = clipboard.get_image()
        .map_err(|e| format!("无法读取剪切板图片: {}", e))?;

    // 将图片转换为PNG格式
    let mut png_data = Vec::new();
    {
        let mut encoder = png::Encoder::new(std::io::Cursor::new(&mut png_data), image.width as u32, image.height as u32);
        encoder.set_color(png::ColorType::Rgba);
        encoder.set_depth(png::BitDepth::Eight);

        let mut writer = encoder.write_header().map_err(|e| format!("PNG头写入失败: {}", e))?;
        writer.write_image_data(&image.bytes).map_err(|e| format!("PNG数据写入失败: {}", e))?;
    }

    // 转换为base64
    let base64_data = general_purpose::STANDARD.encode(&png_data);

    println!("✅ 剪切板图片转换完成，PNG大小: {} 字节，base64长度: {}", png_data.len(), base64_data.len());

    Ok(base64_data)
}

/// 复制文本到剪切板
fn copy_to_clipboard(text: &str) -> Result<(), String> {
    use arboard::Clipboard;

    let mut clipboard = Clipboard::new()
        .map_err(|e| format!("无法访问剪切板: {}", e))?;

    clipboard.set_text(text)
        .map_err(|e| format!("无法写入剪切板: {}", e))?;

    Ok(())
}

/// 处理清空文本
unsafe fn handle_clear_text(hwnd: HWND) {
    println!("🧹 清空AI响应文本和截图数据");

    // 清空AI响应
    AI_RESPONSE.clear();
    AI_STATUS.clear(); // 也清空状态，让窗口完全空白

    // 清空截图数据
    SCREENSHOT_DATA.clear();

    // 重置滚动偏移
    SCROLL_OFFSET = 0;
    TOTAL_TEXT_HEIGHT = 0;

    // 重绘窗口
    InvalidateRect(hwnd, None, TRUE);
    // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它

    println!("✅ 内容清空完成");
}

/// 处理复制窗口文字
unsafe fn handle_copy_text(hwnd: HWND) {
    println!("📋 复制窗口显示的所有文字");

    // 获取要复制的文本内容
    let text_to_copy = if !AI_RESPONSE.is_empty() {
        AI_RESPONSE.clone()
    } else if !AI_STATUS.is_empty() {
        AI_STATUS.clone()
    } else {
        println!("⚠️ 窗口中没有文字可复制");
        return;
    };

    // 复制到剪切板
    match copy_to_clipboard(&text_to_copy) {
        Ok(()) => {
            println!("✅ 文字已复制到剪切板，长度: {} 字符", text_to_copy.len());
        }
        Err(e) => {
            println!("❌ 复制到剪切板失败: {}", e);
        }
    }
}

/// 处理Windows自带截图 (Windows 7兼容版本)
unsafe fn handle_windows_screenshot() {
    use crate::is_uiaccess_supported;

    println!("🖼️ 启动Windows自带截图工具...");

    // 检查系统版本，Windows 8+支持ms-screenclip
    if is_uiaccess_supported() {
        // Windows 8+ 系统，尝试使用现代截图工具
        println!("ℹ️ 检测到Windows 8+系统，使用现代截图工具");

        // 使用Windows 10/11的截图工具
        match std::process::Command::new("ms-screenclip:")
            .spawn()
        {
            Ok(_) => {
                println!("✅ Windows截图工具已启动");
                println!("ℹ️ 请使用Windows截图工具完成截图，然后使用ALT+S发送剪切板内容");
                return;
            }
            Err(e) => {
                println!("❌ 启动Windows截图工具失败: {}", e);
                println!("ℹ️ 尝试备用方法...");

                // 备用方法：使用explorer调用
                match std::process::Command::new("explorer")
                    .arg("ms-screenclip:")
                    .spawn()
                {
                    Ok(_) => {
                        println!("✅ Windows截图工具已启动（备用方法）");
                        println!("ℹ️ 请使用Windows截图工具完成截图，然后使用ALT+S发送剪切板内容");
                        return;
                    }
                    Err(e2) => {
                        println!("❌ 备用方法也失败: {}", e2);
                        println!("ℹ️ 尝试第三种方法...");

                        // 第三种方法：使用cmd调用
                        match std::process::Command::new("cmd")
                            .args(&["/c", "start", "ms-screenclip:"])
                            .spawn()
                        {
                            Ok(_) => {
                                println!("✅ Windows截图工具已启动（第三种方法）");
                                println!("ℹ️ 请使用Windows截图工具完成截图，然后使用ALT+S发送剪切板内容");
                                return;
                            }
                            Err(e3) => {
                                println!("❌ 所有现代方法都失败了: {}", e3);
                                // 继续尝试Windows 7兼容方法
                            }
                        }
                    }
                }
            }
        }
    }

    // Windows 7兼容方法或现代方法失败时的备用方案
    println!("ℹ️ 使用Windows 7兼容的截图方法");

    // 方法1：尝试启动画图工具
    match std::process::Command::new("mspaint")
        .spawn()
    {
        Ok(_) => {
            println!("✅ 画图工具已启动");
            println!("ℹ️ 请在画图中使用 文件 -> 从扫描仪或照相机 或者");
            println!("ℹ️ 使用PrintScreen键截图，然后粘贴到画图中，保存后复制");
        }
        Err(e) => {
            println!("❌ 启动画图工具失败: {}", e);

            // 方法2：尝试启动截图工具（如果存在）
            match std::process::Command::new("SnippingTool")
                .spawn()
            {
                Ok(_) => {
                    println!("✅ 截图工具已启动");
                    println!("ℹ️ 请使用截图工具完成截图，然后使用ALT+S发送剪切板内容");
                }
                Err(e2) => {
                    println!("❌ 启动截图工具失败: {}", e2);
                    println!("ℹ️ Windows 7兼容提示：");
                    println!("   1. 按PrintScreen键截取全屏");
                    println!("   2. 按Alt+PrintScreen截取当前窗口");
                    println!("   3. 然后使用ALT+S发送剪切板内容");
                    println!("   4. 或者手动启动 开始菜单 -> 附件 -> 截图工具");
                }
            }
        }
    }
}

/// 处理截图
unsafe fn handle_screenshot(hwnd: HWND) {
    println!("📸 开始区域截图...");

    // 隐藏主窗口避免干扰截图
    ShowWindow(hwnd, SW_HIDE);

    // 等待一小段时间确保窗口隐藏
    std::thread::sleep(std::time::Duration::from_millis(200));

    // 创建截图捕获器并开始区域选择
    let mut capture = ScreenCapture::new();

    match capture.start_area_capture() {
        Ok(()) => {
            println!("✅ 截图选择界面已启动");
            // 不显示提示信息，让用户专注于截图操作
            // 注意：窗口会在用户完成选择后自动关闭
            // 这里不立即显示主窗口，等待用户操作完成
        }
        Err(e) => {
            println!("❌ 启动截图选择失败: {}", e);
            // 截图启动失败也不显示错误信息，保持界面清洁

            // 显示主窗口
            ShowWindow(hwnd, SW_SHOW);

            // 重绘窗口
            InvalidateRect(hwnd, None, TRUE);
            UpdateWindow(hwnd);
        }
    }
}

/// 执行截图操作 (同步版本)
fn perform_screenshot() -> Result<String, String> {
    println!("📸 执行全屏截图...");

    // 使用Windows API进行全屏截图
    match ScreenCapture::capture_fullscreen() {
        Ok(result) => {
            println!("✅ 全屏截图成功，base64长度: {}", result.base64_data.len());
            Ok(result.base64_data)
        }
        Err(e) => {
            println!("❌ 全屏截图失败: {}", e);
            Err(e)
        }
    }
}

/// 发送剪切板图片数据
unsafe fn send_clipboard_image_data(hwnd: HWND, image_data: String) {
    println!("🖼️ 发送剪切板图片数据...");

    // 检查AI客户端是否可用
    let client = match AI_CLIENT.as_ref() {
        Some(client) => client.clone(),
        None => {
            AI_STATUS = "AI客户端未初始化".to_string();
            InvalidateRect(hwnd, None, TRUE);
            return;
        }
    };

    // 注意：发送状态已经在调用方设置了，这里不需要重复设置

    // 在新线程中发送请求 (同步版本)
    let client_clone = client.clone();
    let hwnd_copy = hwnd;

    std::thread::spawn(move || {
        unsafe {

            // 发送图片到AI
            match client_clone.send_image(&image_data) {
                Ok(response) => {
                    AI_STATUS.clear(); // 清空状态，只显示AI响应
                    AI_RESPONSE = response;

                    // 重置滚动偏移到顶部
                    SCROLL_OFFSET = 0;
                    TOTAL_TEXT_HEIGHT = 0;

                    println!("✅ 剪切板图片AI响应接收成功");

                    // 强制重绘窗口以显示新状态
                    InvalidateRect(hwnd_copy, None, TRUE);
                    // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它
                }
                Err(e) => {
                    AI_STATUS = format!("发送失败: {}", e);
                    AI_RESPONSE.clear();
                    println!("❌ 剪切板图片AI请求失败: {}", e);

                    // 强制重绘窗口以显示错误状态
                    InvalidateRect(hwnd_copy, None, TRUE);
                    // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它
                }
            }

            // 重绘窗口
            InvalidateRect(hwnd_copy, None, TRUE);
        }
    });
}

/// 发送截图数据
unsafe fn send_screenshot_data(hwnd: HWND) {
    println!("📸 发送截图数据...");

    // 检查AI客户端是否可用
    let client = match AI_CLIENT.as_ref() {
        Some(client) => client.clone(),
        None => {
            AI_STATUS = "AI客户端未初始化".to_string();
            InvalidateRect(hwnd, None, TRUE);
            return;
        }
    };

    // 获取截图数据
    let screenshot_data = SCREENSHOT_DATA.clone();

    // 清空截图数据
    SCREENSHOT_DATA.clear();

    // 立即显示发送状态（在主线程中）
    AI_STATUS = "发送中".to_string();
    InvalidateRect(hwnd, None, TRUE);

    // 在新线程中发送请求 (同步版本)
    let client_clone = client.clone();
    let hwnd_copy = hwnd;

    std::thread::spawn(move || {
        unsafe {

            // 发送图片到AI
            match client_clone.send_image(&screenshot_data) {
                Ok(response) => {
                    AI_STATUS.clear(); // 清空状态，只显示AI响应
                    AI_RESPONSE = response;

                    // 重置滚动偏移到顶部
                    SCROLL_OFFSET = 0;
                    TOTAL_TEXT_HEIGHT = 0;

                    println!("✅ 截图AI响应接收成功");

                    // 强制重绘窗口以显示新状态
                    InvalidateRect(hwnd_copy, None, TRUE);
                    // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它
                }
                Err(e) => {
                    AI_STATUS = format!("发送失败: {}", e);
                    AI_RESPONSE.clear();
                    println!("❌ 截图AI请求失败: {}", e);

                    // 强制重绘窗口以显示错误状态
                    InvalidateRect(hwnd_copy, None, TRUE);
                    // UpdateWindow 在新版本的 windows crate 中不可用，我们跳过它
                }
            }

            // 重绘窗口
            InvalidateRect(hwnd_copy, None, TRUE);
        }
    });
}
