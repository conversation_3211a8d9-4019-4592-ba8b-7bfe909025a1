/// AES-GCM加密模块
/// 实现与后端完全兼容的AES-256-GCM加密算法

use aes_gcm::{
    aead::{Aead, KeyInit},
    Aes256Gcm, Nonce, Key
};
use base64::{Engine as _, engine::general_purpose};

/// 加密器结构
pub struct Encryptor {
    cipher: Aes256Gcm,
    nonce: [u8; 12], // GCM模式使用12字节nonce
    debug: bool,
}

impl Encryptor {
    /// 创建新的加密器
    /// 
    /// # 参数
    /// * `key` - 32字节的加密密钥
    /// * `iv` - 12字节的IV/Nonce值
    /// * `debug` - 是否启用调试输出
    pub fn new(key: &str, iv: &str, debug: bool) -> Result<Self, String> {
        // 验证密钥长度
        if key.len() < 32 {
            return Err("加密密钥长度不足，至少需要32字节".to_string());
        }

        // 创建32字节密钥
        let key_bytes = key.as_bytes();
        let key_array: [u8; 32] = key_bytes[..32].try_into()
            .map_err(|_| "密钥转换失败")?;
        let aes_key = Key::<Aes256Gcm>::from_slice(&key_array);

        // 创建12字节nonce（与Go后端兼容的处理方式）
        let iv_bytes = iv.as_bytes();
        let mut nonce_array = [0u8; 12];

        // 如果IV长度不足12字节，用0填充；如果超过12字节，截取前12字节
        let copy_len = iv_bytes.len().min(12);
        nonce_array[..copy_len].copy_from_slice(&iv_bytes[..copy_len]);
        
        // 初始化AES-GCM
        let cipher = Aes256Gcm::new(aes_key);
        
        if debug {
            println!("🔐 加密器初始化成功");
            println!("   密钥长度: {} 字节", key_array.len());
            println!("   Nonce长度: {} 字节", nonce_array.len());
        }
        
        Ok(Self {
            cipher,
            nonce: nonce_array,
            debug,
        })
    }
    
    /// 加密文本
    /// 
    /// # 参数
    /// * `plaintext` - 要加密的明文
    /// 
    /// # 返回
    /// * `Ok(String)` - Base64编码的密文
    /// * `Err(String)` - 加密失败的错误信息
    pub fn encrypt(&self, plaintext: &str) -> Result<String, String> {
        if self.debug {
            println!("🔒 开始加密数据，明文长度: {} 字节", plaintext.len());
        }
        
        // 创建nonce
        let nonce = Nonce::from_slice(&self.nonce);
        
        // 加密数据
        let ciphertext = self.cipher.encrypt(nonce, plaintext.as_bytes())
            .map_err(|e| format!("加密失败: {}", e))?;
        
        // 组合nonce和密文（与Go后端保持一致）
        let mut result = Vec::new();
        result.extend_from_slice(&self.nonce); // 前12字节是nonce
        result.extend_from_slice(&ciphertext); // 后面是密文
        
        // Base64编码
        let encoded = general_purpose::STANDARD.encode(&result);
        
        if self.debug {
            println!("✅ 加密完成，密文长度: {} 字节", encoded.len());
            println!("   前10字符: {}...", &encoded[..10.min(encoded.len())]);
        }
        
        Ok(encoded)
    }
    
    /// 解密文本
    /// 
    /// # 参数
    /// * `ciphertext` - Base64编码的密文
    /// 
    /// # 返回
    /// * `Ok(String)` - 解密后的明文
    /// * `Err(String)` - 解密失败的错误信息
    pub fn decrypt(&self, ciphertext: &str) -> Result<String, String> {
        if self.debug {
            println!("🔓 开始解密数据，密文长度: {} 字节", ciphertext.len());
        }
        
        // Base64解码
        let data = general_purpose::STANDARD.decode(ciphertext)
            .map_err(|e| format!("Base64解码失败: {}", e))?;
        
        // 检查数据长度
        if data.len() < 12 {
            return Err("密文长度不足".to_string());
        }
        
        // 提取nonce和密文
        let (nonce_bytes, cipher_bytes) = data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);
        
        // 解密
        let plaintext_bytes = self.cipher.decrypt(nonce, cipher_bytes)
            .map_err(|e| format!("解密失败: {}", e))?;
        
        // 转换为字符串
        let plaintext = String::from_utf8(plaintext_bytes)
            .map_err(|e| format!("UTF-8转换失败: {}", e))?;
        
        if self.debug {
            println!("✅ 解密完成，明文长度: {} 字节", plaintext.len());
            // 安全地截取前10个字符，考虑UTF-8字符边界
            let preview = plaintext.chars().take(10).collect::<String>();
            println!("   前10字符: {}...", preview);
        }
        
        Ok(plaintext)
    }
    
    /// 测试加密解密功能
    pub fn test(&self) -> Result<(), String> {
        let test_data = "Hello, World! 你好世界！🔐";
        
        println!("🧪 测试加密解密功能...");
        println!("   测试数据: {}", test_data);
        
        // 加密
        let encrypted = self.encrypt(test_data)?;
        println!("   加密结果: {}...", &encrypted[..20.min(encrypted.len())]);
        
        // 解密
        let decrypted = self.decrypt(&encrypted)?;
        println!("   解密结果: {}", decrypted);
        
        // 验证
        if test_data == decrypted {
            println!("✅ 加密解密测试通过");
            Ok(())
        } else {
            Err("加密解密测试失败：数据不匹配".to_string())
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_encryption_decryption() {
        let encryptor = Encryptor::new(
            "test-key-32-bytes-long-for-aes256",
            "test-iv-12b",
            false
        ).unwrap();
        
        let plaintext = "Hello, World!";
        let encrypted = encryptor.encrypt(plaintext).unwrap();
        let decrypted = encryptor.decrypt(&encrypted).unwrap();
        
        assert_eq!(plaintext, decrypted);
    }
    
    #[test]
    fn test_chinese_text() {
        let encryptor = Encryptor::new(
            "test-key-32-bytes-long-for-aes256",
            "test-iv-12b",
            false
        ).unwrap();
        
        let plaintext = "你好，世界！这是中文测试。";
        let encrypted = encryptor.encrypt(plaintext).unwrap();
        let decrypted = encryptor.decrypt(&encrypted).unwrap();
        
        assert_eq!(plaintext, decrypted);
    }
    
    #[test]
    fn test_empty_string() {
        let encryptor = Encryptor::new(
            "test-key-32-bytes-long-for-aes256",
            "test-iv-12b",
            false
        ).unwrap();
        
        let plaintext = "";
        let encrypted = encryptor.encrypt(plaintext).unwrap();
        let decrypted = encryptor.decrypt(&encrypted).unwrap();
        
        assert_eq!(plaintext, decrypted);
    }
    
    #[test]
    fn test_long_text() {
        let encryptor = Encryptor::new(
            "test-key-32-bytes-long-for-aes256",
            "test-iv-12b",
            false
        ).unwrap();
        
        let plaintext = "A".repeat(10000);
        let encrypted = encryptor.encrypt(&plaintext).unwrap();
        let decrypted = encryptor.decrypt(&encrypted).unwrap();
        
        assert_eq!(plaintext, decrypted);
    }
}
